import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateLeadSourceDto } from "./dto/create-lead-source.dto";
import { DeleteLeadSourceDto } from "./dto/delete-lead-source.dto";
import { RestoreLeadSourceDto } from "./dto/restore-lead-source.dto";
import { UpdateLeadSourceDto } from "./dto/update-lead-source.dto";
import { LeadSourceDocument } from "./schema/lead-source.schema";
import { MarketingChannelDocument } from "./schema/channel.schema.dto";
import { GetLeadSourceDto } from "./dto/fetch-lead-source.dto";

@Injectable()
export class LeadSourceService {
    constructor(
        @InjectModel("LeadSource") private readonly leadSourceModel: Model<LeadSourceDocument>,
        @InjectModel("MarketingChannel") private readonly channelModel: Model<MarketingChannelDocument>,
    ) {}

    async createLeadSource(companyId: string, createdBy: string, createLeadSourceDto: CreateLeadSourceDto) {
        try {
            const leadSource = await this.leadSourceModel
                .exists({
                    companyId,
                    name: createLeadSourceDto.name,
                    deleted: false,
                })
                .exec();
            if (leadSource) throw new HttpException("Lead source already exists", HttpStatus.BAD_REQUEST);
            const createdLeadSource = new this.leadSourceModel({
                companyId,
                createdBy,
                ...createLeadSourceDto,
            });
            await createdLeadSource.save();
            return new CreatedResponse({ message: "Lead source created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteLeadSource(userId: string, deleteLeadSourceDto: DeleteLeadSourceDto) {
        try {
            await this.leadSourceModel.findOneAndUpdate(
                { _id: deleteLeadSourceDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );
            return new NoContentResponse({ message: "Lead source deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteLeadSource(companyId: string, deleteLeadSourceDto: DeleteLeadSourceDto) {
        try {
            await this.leadSourceModel.deleteOne({
                _id: deleteLeadSourceDto.id,
                companyId,
            });
            return new NoContentResponse({ message: "Lead source deleted permanently!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreLeadSource(userId: string, restoreLeadSourceDto: RestoreLeadSourceDto) {
        try {
            await this.leadSourceModel.findOneAndUpdate(
                { _id: restoreLeadSourceDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );
            return new OkResponse({ message: "Lead Source restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateLeadSource(userId: string, updateLeadSourceDto: UpdateLeadSourceDto) {
        try {
            // Create a copy of the DTO to avoid modifying the original
            const updateData: any = { ...updateLeadSourceDto };

            // Handle the case where we're updating with a single month cost via actualCost field
            if (updateLeadSourceDto.actualCost) {
                // Get the current lead source to check existing monthlyActualCosts
                const currentLeadSource = await this.leadSourceModel.findOne({
                    _id: updateLeadSourceDto.leadSourceId,
                    deleted: false,
                });

                if (currentLeadSource) {
                    // Initialize the array if it doesn't exist
                    const existingMonthlyCosts = currentLeadSource.actualCost || [];

                    // Create a new array to avoid modifying the original
                    const updatedMonthlyCosts = [...existingMonthlyCosts];

                    // Check if there's an existing entry for this month/year
                    const existingIndex = updatedMonthlyCosts.findIndex(
                        (cost) =>
                            cost.month === updateLeadSourceDto.actualCost.month &&
                            cost.year === updateLeadSourceDto.actualCost.year,
                    );

                    if (existingIndex >= 0) {
                        // Update existing entry
                        updatedMonthlyCosts[existingIndex] = {
                            month: updateLeadSourceDto.actualCost.month,
                            year: updateLeadSourceDto.actualCost.year,
                            cost: updateLeadSourceDto.actualCost.cost,
                        };
                    } else {
                        // Add new entry
                        updatedMonthlyCosts.push({
                            month: updateLeadSourceDto.actualCost.month,
                            year: updateLeadSourceDto.actualCost.year,
                            cost: updateLeadSourceDto.actualCost.cost,
                        });
                    }

                    // Set the monthlyActualCosts in the update data
                    updateData.actualCost = updatedMonthlyCosts;
                }
            }

            const result = await this.leadSourceModel.findOneAndUpdate(
                { _id: updateLeadSourceDto.leadSourceId, deleted: false },
                {
                    $set: updateData,
                },
                { new: true },
            );

            if (!result) throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            return new OkResponse({ message: "Lead source updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLeadSourcesWithChannelAndCampaign(
        companyId: string,
        deleted: boolean,
        getLeadSourceDto: GetLeadSourceDto,
    ) {
        try {
            // Validate input parameters
            if (!companyId) throw new HttpException("Company ID is required", HttpStatus.BAD_REQUEST);

            const limit = getLeadSourceDto.limit || 10;
            const skip = getLeadSourceDto.skip || 0;
            const now = new Date();
            const currentMonth = now.getMonth() + 1; // 1–12
            const currentYear = now.getFullYear();
            // Build match conditions for active sources/campaigns
            const activeCondition =
                getLeadSourceDto?.active === true
                    ? {
                          deleted: false,
                          $or: [
                              {
                                  $and: [
                                      {
                                          $or: [
                                              { startYear: null },
                                              { startYear: 0 },
                                              { startYear: { $exists: false } },
                                              { startYear: undefined },
                                          ],
                                      },
                                      {
                                          $or: [
                                              { startMonth: null },
                                              { startMonth: 0 },
                                              { startMonth: { $exists: false } },
                                              { startMonth: undefined },
                                          ],
                                      },
                                      {
                                          $or: [
                                              { endYear: null },
                                              { endYear: 0 },
                                              { endYear: { $exists: false } },
                                              { endYear: undefined },
                                          ],
                                      },
                                      {
                                          $or: [
                                              { endMonth: null },
                                              { endMonth: 0 },
                                              { endMonth: { $exists: false } },
                                              { endMonth: undefined },
                                          ],
                                      },
                                  ],
                              },
                              {
                                  startYear: { $lte: currentYear },
                                  startMonth: { $lte: currentMonth },
                                  $or: [
                                      { endYear: { $in: [null, 0] } },
                                      { endYear: { $exists: false } },
                                      { endMonth: { $in: [null, 0] } },
                                      { endMonth: { $exists: false } },
                                  ],
                              },
                              {
                                  startYear: { $lte: currentYear },
                                  startMonth: { $lte: currentMonth },
                                  endYear: { $gte: currentYear },
                                  endMonth: { $gte: currentMonth },
                              },
                          ],
                      }
                    : {};

            // Use a more efficient aggregation pipeline
            const leadSource = await this.channelModel.aggregate([
                {
                    $match: {
                        companyId,
                        ...(getLeadSourceDto?.active === true && { deleted: false }),
                    },
                },
                {
                    $project: {
                        _id: 1,
                        channelName: "$name",
                        channelDesc: "$description",
                        order: 1,
                        deleted: 1,
                    },
                },
                {
                    $lookup: {
                        from: "LeadSource",
                        let: { channelId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$channelId", "$$channelId"] },
                                            { $eq: ["$companyId", companyId] },
                                        ],
                                    },
                                    ...activeCondition,
                                },
                            },
                            {
                                $lookup: {
                                    from: "Campaign",
                                    let: { leadSourceId: "$_id" },
                                    pipeline: [
                                        {
                                            $match: {
                                                $expr: {
                                                    $and: [
                                                        { $eq: ["$$leadSourceId", "$leadSourceId"] },
                                                        { $eq: ["$companyId", companyId] },
                                                    ],
                                                },
                                                ...activeCondition,
                                            },
                                        },
                                        {
                                            $project: {
                                                _id: 1,
                                                name: 1,
                                                description: 1,
                                                startMonth: 1,
                                                startYear: 1,
                                                endMonth: 1,
                                                endYear: 1,
                                                spend: 1,
                                                isMonthly: 1,
                                                deleted: 1,
                                                cost: 1,
                                            },
                                        },
                                        {
                                            $sort: { name: 1 },
                                        },
                                    ],
                                    as: "campaigns",
                                },
                            },
                            {
                                $project: {
                                    _id: 1,
                                    deleted: 1,
                                    description: 1,
                                    name: 1,
                                    channelId: 1,
                                    code: 1,
                                    isMonthly: 1,
                                    startMonth: 1,
                                    startYear: 1,
                                    endMonth: 1,
                                    endYear: 1,
                                    campaigns: 1,
                                    cost: 1,
                                },
                            },
                            {
                                $sort: { name: 1 },
                            },
                        ],
                        as: "leadSources",
                    },
                },
                {
                    $sort: {
                        order: 1,
                    },
                },
            ]);

            // Count total for pagination
            const totalCount = await this.channelModel.countDocuments({
                companyId,
                ...(getLeadSourceDto?.active === true && { deleted: false }),
            });

            return new OkResponse({
                leadSource,
                pagination: {
                    total: totalCount,
                    page: skip,
                    limit,
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLeadSourceById(userId: string, companyId: string, leadSourceId: string, deleted: boolean) {
        try {
            const leadSource = await this.leadSourceModel.findOne({ _id: leadSourceId, companyId, deleted });
            return new OkResponse({ leadSource });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // async addDefaultLeadSourceSetting(companyId: string, memberId: string) {
    //     try {
    //         const data = await buildInLeadSourceSetting.map((value) => ({
    //             ...value,
    //             companyId,
    //             createdBy: memberId,
    //         }));
    //         await this.leadSourceModel.insertMany(data);
    //         return new CreatedResponse({ message: "Lead Source Setting created successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    /**
     * Get advertising costs for channels, lead sources, and campaigns
     * @param companyId - Company ID
     * @param month - Month to filter (1-12)
     * @param year - Year to filter
     * @returns Advertising costs data with estimated and actual costs
     */
    async advertisingCost(companyId: string, month?: number, year?: number) {
        try {
            const now = new Date();
            // Default to last month if not provided
            const targetMonth = month || (now.getMonth() === 0 ? 12 : now.getMonth()); // 1-12
            const targetYear = year || (now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear());

            // Build match conditions for active sources/campaigns
            const activeCondition = {
                deleted: false,

                $or: [
                    {
                        $and: [
                            {
                                $or: [
                                    { startYear: null },
                                    { startYear: 0 },
                                    { startYear: { $exists: false } },
                                    { startYear: undefined },
                                ],
                            },
                            {
                                $or: [
                                    { startMonth: null },
                                    { startMonth: 0 },
                                    { startMonth: { $exists: false } },
                                    { startMonth: undefined },
                                ],
                            },
                            {
                                $or: [
                                    { endYear: null },
                                    { endYear: 0 },
                                    { endYear: { $exists: false } },
                                    { endYear: undefined },
                                ],
                            },
                            {
                                $or: [
                                    { endMonth: null },
                                    { endMonth: 0 },
                                    { endMonth: { $exists: false } },
                                    { endMonth: undefined },
                                ],
                            },
                        ],
                    },
                    {
                        startYear: { $lte: targetYear },
                        startMonth: { $lte: targetMonth },
                        $or: [
                            { endYear: { $in: [null, 0] } },
                            { endYear: { $exists: false } },
                            { endMonth: { $in: [null, 0] } },
                            { endMonth: { $exists: false } },
                        ],
                    },
                    {
                        startYear: { $lte: targetYear },
                        startMonth: { $lte: targetMonth },
                        endYear: { $gte: targetYear },
                        endMonth: { $gte: targetMonth },
                    },
                ],
            };

            const advertisingCosts = await this.channelModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "LeadSource",
                        let: { channelId: "$_id" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$channelId", "$$channelId"] },
                                            { $eq: ["$companyId", companyId] },
                                        ],
                                    },
                                },
                            },
                            {
                                $addFields: {
                                    estimatedCost: {
                                        $let: {
                                            vars: {
                                                // Create proper date objects
                                                startDate: {
                                                    $cond: [
                                                        {
                                                            $and: [
                                                                { $ne: ["$startYear", null] },
                                                                { $ne: ["$startMonth", null] },
                                                            ],
                                                        },
                                                        {
                                                            $dateFromParts: {
                                                                year: "$startYear",
                                                                month: "$startMonth",
                                                                day: 1,
                                                            },
                                                        },
                                                        null,
                                                    ],
                                                },
                                                // Use end date if available, otherwise current date
                                                endDate: {
                                                    $cond: [
                                                        {
                                                            $and: [
                                                                {
                                                                    $ne: [
                                                                        { $ifNull: ["$endYear", null] },
                                                                        null,
                                                                    ],
                                                                },
                                                                {
                                                                    $ne: [
                                                                        { $ifNull: ["$endMonth", null] },
                                                                        null,
                                                                    ],
                                                                },
                                                            ],
                                                        },
                                                        {
                                                            $dateFromParts: {
                                                                year: "$endYear",
                                                                month: "$endMonth",
                                                                day: 1,
                                                            },
                                                        },
                                                        {
                                                            $dateFromParts: {
                                                                year: now.getFullYear(),
                                                                month: now.getMonth() + 1,
                                                                day: 1,
                                                            },
                                                        },
                                                    ],
                                                },
                                            },
                                            in: {
                                                $cond: [
                                                    { $eq: ["$isMonthly", true] },
                                                    "$cost", // Case 1: Full cost for monthly items

                                                    // Case 2 & 3: One-time costs (with or without end date)
                                                    {
                                                        $cond: [
                                                            { $eq: ["$$startDate", null] },
                                                            0, // Only fallback if no start date
                                                            {
                                                                $let: {
                                                                    vars: {
                                                                        totalMonths: {
                                                                            $add: [
                                                                                1, // Include both months
                                                                                {
                                                                                    $subtract: [
                                                                                        {
                                                                                            $add: [
                                                                                                {
                                                                                                    $multiply:
                                                                                                        [
                                                                                                            {
                                                                                                                $year: "$$endDate",
                                                                                                            },
                                                                                                            12,
                                                                                                        ],
                                                                                                },
                                                                                                {
                                                                                                    $month: "$$endDate",
                                                                                                },
                                                                                            ],
                                                                                        },
                                                                                        {
                                                                                            $add: [
                                                                                                {
                                                                                                    $multiply:
                                                                                                        [
                                                                                                            {
                                                                                                                $year: "$$startDate",
                                                                                                            },
                                                                                                            12,
                                                                                                        ],
                                                                                                },
                                                                                                {
                                                                                                    $month: "$$startDate",
                                                                                                },
                                                                                            ],
                                                                                        },
                                                                                    ],
                                                                                },
                                                                            ],
                                                                        },
                                                                    },
                                                                    in: {
                                                                        $cond: [
                                                                            { $lte: ["$$totalMonths", 0] }, // Handle date math errors
                                                                            "$$totalMonths",
                                                                            {
                                                                                $divide: [
                                                                                    "$cost",
                                                                                    "$$totalMonths",
                                                                                ],
                                                                            },
                                                                        ],
                                                                    },
                                                                },
                                                            },
                                                        ],
                                                    },
                                                ],
                                            },
                                        },
                                    },
                                    actualCost: {
                                        $let: {
                                            vars: {
                                                matchingCost: {
                                                    $filter: {
                                                        input: { $ifNull: ["$actualCost", []] },
                                                        as: "cost",
                                                        cond: {
                                                            $and: [
                                                                { $eq: ["$$cost.month", targetMonth] },
                                                                { $eq: ["$$cost.year", targetYear] },
                                                            ],
                                                        },
                                                    },
                                                },
                                            },
                                            in: {
                                                $cond: [
                                                    { $gt: [{ $size: "$$matchingCost" }, 0] },
                                                    { $arrayElemAt: ["$$matchingCost.cost", 0] },
                                                    0,
                                                ],
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                $lookup: {
                                    from: "Campaign",
                                    let: { leadSourceId: "$_id" },
                                    pipeline: [
                                        {
                                            $match: {
                                                $expr: {
                                                    $and: [
                                                        { $eq: ["$$leadSourceId", "$leadSourceId"] },
                                                        { $eq: ["$companyId", companyId] },
                                                    ],
                                                },
                                                ...activeCondition,
                                            },
                                        },
                                        {
                                            $addFields: {
                                                estimatedCost: {
                                                    $let: {
                                                        vars: {
                                                            // Create proper date objects
                                                            startDate: {
                                                                $cond: [
                                                                    {
                                                                        $and: [
                                                                            { $ne: ["$startYear", null] },
                                                                            { $ne: ["$startMonth", null] },
                                                                        ],
                                                                    },
                                                                    {
                                                                        $dateFromParts: {
                                                                            year: "$startYear",
                                                                            month: "$startMonth",
                                                                            day: 1,
                                                                        },
                                                                    },
                                                                    null,
                                                                ],
                                                            },
                                                            // Use end date if available, otherwise current date
                                                            endDate: {
                                                                $cond: [
                                                                    {
                                                                        $and: [
                                                                            {
                                                                                $ne: [
                                                                                    {
                                                                                        $ifNull: [
                                                                                            "$endYear",
                                                                                            null,
                                                                                        ],
                                                                                    },
                                                                                    null,
                                                                                ],
                                                                            },
                                                                            {
                                                                                $ne: [
                                                                                    {
                                                                                        $ifNull: [
                                                                                            "$endMonth",
                                                                                            null,
                                                                                        ],
                                                                                    },
                                                                                    null,
                                                                                ],
                                                                            },
                                                                        ],
                                                                    },
                                                                    {
                                                                        $dateFromParts: {
                                                                            year: "$endYear",
                                                                            month: "$endMonth",
                                                                            day: 1,
                                                                        },
                                                                    },
                                                                    {
                                                                        $dateFromParts: {
                                                                            year: now.getFullYear(),
                                                                            month: now.getMonth() + 1,
                                                                            day: 1,
                                                                        },
                                                                    },
                                                                ],
                                                            },
                                                        },
                                                        in: {
                                                            $cond: [
                                                                { $eq: ["$isMonthly", true] },
                                                                "$cost", // Case 1: Full cost for monthly items

                                                                // Case 2 & 3: One-time costs (with or without end date)
                                                                {
                                                                    $cond: [
                                                                        { $eq: ["$$startDate", null] },
                                                                        0, // Only fallback if no start date
                                                                        {
                                                                            $let: {
                                                                                vars: {
                                                                                    totalMonths: {
                                                                                        $add: [
                                                                                            1, // Include both months
                                                                                            {
                                                                                                $subtract: [
                                                                                                    {
                                                                                                        $add: [
                                                                                                            {
                                                                                                                $multiply:
                                                                                                                    [
                                                                                                                        {
                                                                                                                            $year: "$$endDate",
                                                                                                                        },
                                                                                                                        12,
                                                                                                                    ],
                                                                                                            },
                                                                                                            {
                                                                                                                $month: "$$endDate",
                                                                                                            },
                                                                                                        ],
                                                                                                    },
                                                                                                    {
                                                                                                        $add: [
                                                                                                            {
                                                                                                                $multiply:
                                                                                                                    [
                                                                                                                        {
                                                                                                                            $year: "$$startDate",
                                                                                                                        },
                                                                                                                        12,
                                                                                                                    ],
                                                                                                            },
                                                                                                            {
                                                                                                                $month: "$$startDate",
                                                                                                            },
                                                                                                        ],
                                                                                                    },
                                                                                                ],
                                                                                            },
                                                                                        ],
                                                                                    },
                                                                                },
                                                                                in: {
                                                                                    $cond: [
                                                                                        {
                                                                                            $lte: [
                                                                                                "$$totalMonths",
                                                                                                0,
                                                                                            ],
                                                                                        }, // Handle date math errors
                                                                                        "$cost",
                                                                                        {
                                                                                            $divide: [
                                                                                                "$cost",
                                                                                                "$$totalMonths",
                                                                                            ],
                                                                                        },
                                                                                    ],
                                                                                },
                                                                            },
                                                                        },
                                                                    ],
                                                                },
                                                            ],
                                                        },
                                                    },
                                                },
                                                actualCost: {
                                                    $let: {
                                                        vars: {
                                                            matchingCost: {
                                                                $filter: {
                                                                    input: { $ifNull: ["$actualCost", []] },
                                                                    as: "cost",
                                                                    cond: {
                                                                        $and: [
                                                                            {
                                                                                $eq: [
                                                                                    "$$cost.month",
                                                                                    targetMonth,
                                                                                ],
                                                                            },
                                                                            {
                                                                                $eq: [
                                                                                    "$$cost.year",
                                                                                    targetYear,
                                                                                ],
                                                                            },
                                                                        ],
                                                                    },
                                                                },
                                                            },
                                                        },
                                                        in: {
                                                            $cond: [
                                                                { $gt: [{ $size: "$$matchingCost" }, 0] },
                                                                { $arrayElemAt: ["$$matchingCost.cost", 0] },
                                                                0,
                                                            ],
                                                        },
                                                    },
                                                },
                                            },
                                        },
                                        {
                                            $project: {
                                                _id: 1,
                                                name: 1,
                                                estimatedCost: { $round: ["$estimatedCost", 2] },
                                                actualCost: { $round: ["$actualCost", 2] },
                                                isMonthly: 1,
                                            },
                                        },
                                    ],
                                    as: "campaigns",
                                },
                            },
                            {
                                $project: {
                                    _id: 1,
                                    name: 1,
                                    estimatedCost: { $round: ["$estimatedCost", 2] },
                                    actualCost: { $round: ["$actualCost", 2] },
                                    campaigns: 1,
                                    isMonthly: 1,
                                    startYear: 1,
                                    startMonth: 1,
                                    endYear: 1,
                                    endMonth: 1,
                                },
                            },
                        ],
                        as: "leadSources",
                    },
                },
                {
                    $addFields: {
                        estimatedCost: {
                            $sum: {
                                $map: {
                                    input: "$leadSources",
                                    as: "leadSource",
                                    in: {
                                        $add: [
                                            "$$leadSource.estimatedCost",
                                            { $sum: "$$leadSource.campaigns.estimatedCost" },
                                        ],
                                    },
                                },
                            },
                        },
                        actualCost: {
                            $sum: {
                                $map: {
                                    input: "$leadSources",
                                    as: "leadSource",
                                    in: {
                                        $add: [
                                            "$$leadSource.actualCost",
                                            { $sum: "$$leadSource.campaigns.actualCost" },
                                        ],
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        leadSources: {
                            $filter: {
                                input: "$leadSources",
                                as: "leadSource",
                                cond: {
                                    $or: [
                                        // Case 1: Has start date but no end date (ongoing)
                                        {
                                            $and: [
                                                { $ifNull: ["$$leadSource.startYear", false] },
                                                { $ifNull: ["$$leadSource.startMonth", false] },
                                                { $eq: [{ $ifNull: ["$$leadSource.endYear", null] }, null] },
                                                { $eq: [{ $ifNull: ["$$leadSource.endMonth", null] }, null] },
                                            ],
                                        },
                                        // Case 2: Within date range
                                        {
                                            $and: [
                                                { $lte: ["$$leadSource.startYear", targetYear] },
                                                { $lte: ["$$leadSource.startMonth", targetMonth] },
                                                { $gte: ["$$leadSource.endYear", targetYear] },
                                                { $gte: ["$$leadSource.endMonth", targetMonth] },
                                            ],
                                        },

                                        // Case 3: Lead source has campaigns (regardless of active status)
                                        { $gt: [{ $size: "$$leadSource.campaigns" }, 0] },
                                    ],
                                },
                            },
                        },
                        estimatedCost: { $round: ["$estimatedCost", 2] },
                        actualCost: { $round: ["$actualCost", 2] },
                    },
                },
                {
                    $match: {
                        "leadSources.0": { $exists: true },
                    },
                },
                {
                    $sort: { order: 1 },
                },
            ]);

            return new OkResponse({
                advertisingCosts,
                filters: {
                    month: targetMonth,
                    year: targetYear,
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
