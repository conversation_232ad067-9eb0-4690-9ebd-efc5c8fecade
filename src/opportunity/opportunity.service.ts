import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Connection, Model } from "mongoose";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import {
    shortenDateTime,
    formatDate,
    getTextChange,
    createPO,
    profitScoreCalc,
    roundTo1,
    findCurrentWage,
    formatNumberToCommaS,
    roundTo2,
    calculateTotalCosts,
} from "src/shared/helpers/logics";
import CreatedResponse from "src/shared/http/response/created.http";
import UpdateFailedResponse from "src/shared/http/response/failed.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateNewActionDto } from "./dto/create-new-action.dto";
import { CreateOpportunityCommentDto } from "./dto/create-opportunity-comment.dto";
import { DeleteOpportunityCommentDto } from "./dto/delete-opportunity-comment.dto";
import { DeleteOpportunityDto } from "./dto/delete-opportunity.dto";
import { LostUnLostOpportunityDto } from "./dto/lost-unlost-opporuntiy.dto";
import { PermDeleteOpportunityDto } from "./dto/perm-delete-opportunity.dto";
import { UpdateChecklistDto } from "./dto/update-checklist.dto";
import { UpdateCompleteStageDto } from "./dto/update-complete-stage.dto";
import { UpdateOppStatusDto } from "./dto/update-opp-status.dto";
import { UpdateOppActivityDto } from "./dto/update-opportunity-activity.dto";
import { UpdateOpportunityCommentDto } from "./dto/update-opportunity-comment.dto";
import { UpdateOpportunityDateDto } from "./dto/update-opportunity-date.dto";
import { UpdateOppStageDto } from "./dto/update-opportunity-stage.dto";
import { CommissionModificationDocument } from "./schema/opp-commission.schema";
import { OpportunityActivityDocument } from "./schema/opportunity-activity-log.schema";
import { CreateWarrantyOpportunityDto } from "./dto/create-warranty-opportunity.dto";
import { GetOpportunityDto } from "./dto/get-opportunity.dto";
import { UpdateOpportunityDto } from "./dto/update-opportunity.dto";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { AddPieceWorkDto } from "src/crm/dto/add-modified-piecework.dto";
import { FieldTypeEnum } from "src/crm/enum/field-type.enum";
import { LeadDocument } from "src/lead/schema/lead.schema";
import { PeriodEnum } from "src/pay-schedule/enum/period.enum";
import { OrderDocument } from "src/project/schema/order.schema";
import { ProjectDocument } from "src/project/schema/project.schema";
import { TaxJurisdictionDocument } from "src/project/schema/tax-jurisdiction.schema";
import { AddModificationCommissionDto } from "./dto/add-modification.dto";
import { ChangeOrderDto } from "./dto/change-order.dto";
import { DeleteOpportunityCheckpointDto } from "./dto/delete-opportunity-checkpoint.dto";
import { GetOldOpportunityDto } from "./dto/get-old-opp.dto";
import { SearchOpportunityDto } from "./dto/search-opps.dto";
import { UpdateModificationCommissionDto } from "./dto/update-modification.dto";
import { UpdateOpportunityCheckpointDto } from "./dto/update-opportunity-checkpoint.dto";
import { CompanyPayDocument } from "src/company/schema/company-pay.schema";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { ProjectTypeDocument } from "src/project/schema/project-type.schema";
import { CrmCheckpointDocument } from "src/crm/schema/crm-checkpoint.schema";
import { CrmStepDocument } from "src/crm/schema/crm-step.schema";
import * as https from "https";
import { FormDocument } from "./schema/form.schema";
import { UpdateOpportunityFormDto } from "./dto/update-opportunity-form.dto";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PositionService } from "src/position/position.service";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { CreateOpportunityFormDto } from "./dto/create-opportunity-form.dto";
import { FormBuilderDocument } from "src/form-builder/schema/form-builder.schema";
import { MailService } from "src/mail/mail.service";
import { MemberDocument } from "src/company/schema/member.schema";
import { OpportunityStatusEnum } from "./enum/opportunityStatus.enum";
import { ContactDocument } from "src/contacts/schema/contact.schema";
import { ContactTypeEnum } from "src/contacts/enum/contact.enum";
import { ActivityLogDocument } from "src/activity-log/schema/activity-log.schema";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";

@Injectable()
export class OpportunityService {
    constructor(
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("ActivityLog")
        private readonly activityModel: Model<ActivityLogDocument>,
        @InjectModel("CommissionModification")
        private readonly commissionModificationModel: Model<CommissionModificationDocument>,
        @InjectModel("Lead") private readonly leadModel: Model<LeadDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("Contact") private readonly contactModel: Model<ContactDocument>,
        @InjectModel("Project") private readonly projectModel: Model<ProjectDocument>,
        @InjectModel("TaxJurisdiction") private readonly taxModel: Model<TaxJurisdictionDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("CompanyPay")
        private readonly payModel: Model<CompanyPayDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("CrmStep") private readonly crmStepModel: Model<CrmStepDocument>,
        @InjectModel("CrmCheckpoint") private readonly crmCheckpointModel: Model<CrmCheckpointDocument>,
        @InjectModel("Forms") private readonly formsModel: Model<FormDocument>,
        @InjectModel("FormBuilder") private readonly formBuilderModel: Model<FormBuilderDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        private readonly positionService: PositionService,
        private readonly mailService: MailService,
    ) {}

    async createOpportunity(
        memberId: string,
        companyId: string,
        createOpportunityDto: CreateWarrantyOpportunityDto,
        // positionCheck: boolean,
        // converted: boolean,
        warrantyType: boolean,
    ) {
        let session;
        const { leadId, currentDate, createLead, contactId } = createOpportunityDto;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();
            const opportunityId = randomUUID();

            const name =
                createOpportunityDto?.lastName && createOpportunityDto?.lastName !== ""
                    ? createOpportunityDto.lastName
                    : createOpportunityDto.firstName;
            const PO = createPO(name, createOpportunityDto?.street);
            const num = await this.getNum(companyId, PO, warrantyType);
            const leadDataToUpdate: any = {};
            let comments = [];

            if (createLead) {
                if (leadId) {
                    await this.leadModel.updateOne(
                        { _id: leadId, companyId },
                        {
                            $set: {
                                lostReason: "New Opportunity created",
                                lostDate: currentDate,
                                status: "lost",
                                lostBy: memberId,
                            },
                        },
                        { session },
                    );
                }
                // creating new lead
                const stageData = await this.crmStageModel.findOne(
                    {
                        companyId: companyId,
                        deleted: false,
                        stageGroup: StageGroupEnum.Leads,
                        sequence: 1,
                    },
                    { _id: 1, defaultCsrId: 1 },
                );

                const createdLead = new this.leadModel({
                    contactId,
                    companyId,
                    createdBy: memberId,
                    status: "converted",
                    stageId: stageData._id,
                    newLeadDate: createOpportunityDto?.newLeadDate,
                    csrId: createOpportunityDto?.csrId || stageData.defaultCsrId,
                    leadSourceId: createOpportunityDto?.leadSourceId,
                    campaignId: createOpportunityDto?.campaignId,
                    workType: createOpportunityDto?.oppType,
                    referredBy: createOpportunityDto?.referredBy,
                    oppId: opportunityId,
                    oppDate: currentDate,
                });
                await createdLead.save({ session });

                await this.activityModel.updateOne(
                    {
                        moduleId: contactId,
                        moduleType: "contact",
                        companyId,
                    },
                    {
                        $push: {
                            activities: {
                                _id: randomUUID(),
                                body: "created a New Lead with Opportunity",
                                createdBy: memberId,
                                createdAt: new Date(currentDate).toISOString(),
                            },
                        },
                    },
                    { session },
                );
            } else {
                if (leadId) {
                    const { modifiedCount } = await this.leadModel.updateOne(
                        { _id: leadId, companyId, oppId: { $exists: false } },
                        { $set: { oppId: opportunityId, oppDate: currentDate, status: "converted" } },
                        { session },
                    );
                    if (modifiedCount > 0) {
                        await this.activityModel.updateOne(
                            {
                                moduleId: contactId,
                                moduleType: "contact",
                                companyId,
                            },
                            {
                                $push: {
                                    activities: {
                                        _id: randomUUID(),
                                        body: "converted Lead to Opportunity",
                                        createdBy: memberId,
                                        createdAt: new Date(currentDate).toISOString(),
                                    },
                                },
                            },
                            { session },
                        );
                    }
                } else {
                    const leadData = await this.leadModel.findOne(
                        { contactId, companyId, deleted: false, status: "active" },
                        { sort: { newLeadDate: -1 } },
                    );
                    if (leadData) {
                        await this.leadModel.updateOne(
                            { _id: leadData._id },
                            { $set: { oppId: opportunityId, oppDate: currentDate, status: "converted" } },
                            { session },
                        );
                    }
                }
            }

            // creating new activity data
            const createdOppActivity = new this.activityModel({
                moduleId: opportunityId,
                moduleType: "opportunity",
                companyId,
                activities: [
                    {
                        _id: randomUUID(),
                        body: "created New Opportunity",
                        createdBy: createOpportunityDto.createdBy,
                        createdAt: new Date(currentDate).toISOString(),
                    },
                ],
            });
            await createdOppActivity.save({ session });
            // }
            comments = [
                ...comments,
                ...createOpportunityDto.comments,
                {
                    _id: randomUUID(),
                    type: "notes",
                    createdBy: memberId,
                    body: createOpportunityDto?.oppNotes,
                    createdAt: new Date(currentDate).toISOString(),
                },
            ];

            // if there is only one tax for 1 state then add that by default for opp
            const allTax = await this.taxModel.find({
                companyId,
                state: createOpportunityDto?.state,
                deleted: false,
            });
            let taxJurisdiction;
            if (allTax.length === 1) taxJurisdiction = allTax[0]._id;

            // delete firstname & lastname
            delete createOpportunityDto.firstName;
            delete createOpportunityDto.lastName;
            delete createOpportunityDto.comments;

            const checkpointActivity: any = {
                newLeadDate: {
                    created: new Date(createOpportunityDto.newLeadDate).toISOString(),
                },
                oppDate: {
                    created: new Date(createOpportunityDto?.oppDate).toISOString(),
                },
            };

            if (warrantyType) {
                createOpportunityDto["saleDate"] = createOpportunityDto.oppDate;
                checkpointActivity.saleDate = {
                    created: new Date(createOpportunityDto.oppDate).toISOString(),
                };
            }

            if (createOpportunityDto?.needsAssessmentDate) {
                checkpointActivity.needsAssessmentDate = {
                    created: new Date(createOpportunityDto.needsAssessmentDate).toISOString(),
                };
            }
            const createdOpportunity = new this.opportunityModel({
                _id: opportunityId,
                PO,
                num,
                type: "new",
                status: "active",
                companyId,
                salesPersonHistory: createOpportunityDto?.salesPerson
                    ? [createOpportunityDto?.salesPerson]
                    : [],
                checkpointActivity,
                taxJurisdiction,
                comments,
                warrantyType,
                ...createOpportunityDto,
                ...leadDataToUpdate,
            });
            await createdOpportunity.save({ session });

            // updating contact status
            await this.contactModel.updateOne(
                { _id: createOpportunityDto.contactId, type: { $ne: ContactTypeEnum.CLIENT } },
                {
                    $set: {
                        type: ContactTypeEnum.PROSPECT,
                    },
                },
                { session },
            );

            // ending session of mongodb
            await session.commitTransaction();
            session.endSession();

            return new CreatedResponse({
                message: "Opportunity created successfully!",
                oppId: opportunityId,
            });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateOpportunity(
        companyId: string,
        updateOpportunityDto: UpdateOpportunityDto,
        user: any,
        // positionCheck: boolean,
    ) {
        try {
            // check for duplicate PO & Num
            const opportunity = await this.opportunityModel.findOne(
                { _id: updateOpportunityDto.opportunityId, deleted: false },
                { acceptedProjectId: 1 },
            );

            if (!opportunity) {
                throw new HttpException("Opportunity not found!", HttpStatus.NOT_FOUND);
            }

            //TODO: for now role check is removed
            // If acceptedProjectId exists, Only owner & admin are allowed to update.
            // if (
            //     opportunity.acceptedProjectId &&
            //     ![UserRolesEnum.Owner, UserRolesEnum.Admin].includes(user.role)
            // ) {
            //     throw new HttpException("You are unauthorized to perform this request", HttpStatus.FORBIDDEN);
            // }

            const opps = await this.opportunityModel.find({
                _id: { $ne: updateOpportunityDto.opportunityId },
                PO: updateOpportunityDto.PO,
                num: updateOpportunityDto.num,
                companyId,
                deleted: { $ne: true },
            });

            if (opps.length > 0) {
                const newPO =
                    updateOpportunityDto.PO +
                    "-" +
                    (parseInt(updateOpportunityDto.num) + 1).toString().padStart(2, "0");

                throw new HttpException(
                    `Given PO: ${updateOpportunityDto.PO}-${updateOpportunityDto.num} already exists!, Try ${newPO}`,
                    HttpStatus.BAD_REQUEST,
                );
            }

            // if there is only one tax for 1 state then add that by default for opp
            let updateQuery;
            if (updateOpportunityDto?.state === "ID") {
                const tax = await this.taxModel.findOne({
                    companyId,
                    state: "ID",
                    deleted: false,
                });
                updateQuery = { ...updateOpportunityDto, taxJurisdiction: tax._id };
            } else updateQuery = { ...updateOpportunityDto };

            // budget score update
            if (updateOpportunityDto?.financeFee > 0) {
                const { priceTotals } = await this.orderModel.findOne({
                    oppId: updateOpportunityDto.opportunityId,
                    companyId,
                    deleted: false,
                });
                const { jobTotal, mTotal, lTotal, commission } = priceTotals;
                updateQuery["budgetScore"] = profitScoreCalc(
                    jobTotal,
                    mTotal,
                    lTotal,
                    commission,
                    updateOpportunityDto?.financeFee,
                );
            }

            const result = await this.opportunityModel.updateOne(
                { _id: updateOpportunityDto.opportunityId, deleted: false },
                {
                    $set: updateQuery,
                    $addToSet: {
                        salesPersonHistory: {
                            $each: [updateOpportunityDto.salesPerson, updateOpportunityDto.projectManager],
                        },
                    },
                },
                { new: true },
            );

            // Update contact's salesPerson and projectManager in a single query if needed
            if (
                updateOpportunityDto?.salesPerson ||
                updateOpportunityDto?.projectManager ||
                updateOpportunityDto?.csrId
            ) {
                const updateFields = {};
                if (updateOpportunityDto.csrId) {
                    updateFields["csrId"] = updateOpportunityDto.csrId;
                }
                if (updateOpportunityDto?.salesPerson) {
                    updateFields["salesPersonId"] = updateOpportunityDto.salesPerson;
                }
                if (updateOpportunityDto?.projectManager) {
                    updateFields["projectManagerId"] = updateOpportunityDto.projectManager;
                }

                await this.contactModel.updateOne(
                    { _id: updateOpportunityDto.contactId },
                    { $set: updateFields },
                );
            }

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.NOT_MODIFIED);
            }

            // this.updateOpportunityStage(userId, {
            //     memberId: updateOpportunityDto.editedBy,
            //     id: updateOpportunityDto.opportunityId,
            //     newStage: updateOpportunityDto.stage,
            //     currDate: updateOpportunityDto.currDate,
            // });
            return new OkResponse({ message: "Opportunity updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOpportunity(
        companyId: string,
        memberId: string,
        permission: PermissionsEnum,
        deleted: boolean,
        stageGroup: StageGroupEnum,
        getOpportunityDto: GetOpportunityDto,
        positionSymbol?: string,
    ) {
        try {
            // To get list of members managed by logged in member
            const { members } = await this.positionService.getManagedMembersInternal(
                memberId,
                companyId,
                permission,
                positionSymbol,
            );

            const { status, salesPerson, projectManager, oppType, crew } = getOpportunityDto;
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);
            const limit = getOpportunityDto.limit || 10;
            const offset = limit * (getOpportunityDto.skip || 0);
            const query: any = {
                $and: [
                    {
                        $or: [
                            {
                                "stageData.code": "completed",
                                jobCompletedDate: { $gte: thirtyDaysAgo },
                            },
                            { "stageData.code": { $ne: "completed" } },
                        ],
                    },
                ],
                $or: [
                    { salesPerson: { $in: members } },
                    { projectManager: { $in: members } },
                    { "workingCrew.id": { $in: members } },
                ],
                companyId,
                deleted,
                ...(status !== undefined && { status }),
                ...(stageGroup !== undefined && { "stageData.stageGroup": stageGroup }),
                ...(salesPerson?.length && { salesPerson: { $in: salesPerson } }),
                ...(projectManager?.length && { projectManager: { $in: projectManager } }),
                ...(oppType?.length && { oppType: { $in: oppType } }),
                ...(crew?.length && { "workingCrew.id": { $in: crew } }),
            };

            const variables = await this.companySettingModel.findOne(
                { companyId },
                { weekEndDays: 1, dailyOH: 1 },
            );
            const weekEndDays = variables?.weekEndDays || []; // Your weekend days array (e.g., ["Saturday", "Sunday"])

            const dayNameToNumber = {
                Sunday: 1,
                Monday: 2,
                Tuesday: 3,
                Wednesday: 4,
                Thursday: 5,
                Friday: 6,
                Saturday: 7,
            };
            const weekEndDayNumbers = weekEndDays.map((dayName) => dayNameToNumber[dayName]).filter(Boolean);

            const pipeline =
                stageGroup === StageGroupEnum.Operations
                    ? [
                          {
                              $project: {
                                  _id: 1,
                                  companyId: 1,
                                  deleted: 1,
                                  PO: 1,
                                  oppType: 1,
                                  num: 1,
                                  firstName: 1,
                                  lastName: 1,
                                  stage: 1,
                                  oppNotes: 1,
                                  jobNote: 1,
                                  salesPerson: 1,
                                  projectManager: 1,
                                  contactId: 1,
                                  createdBy: 1,
                                  opportunityId: 1,
                                  orderId: 1,
                                  acceptedType: 1,
                                  acceptedProjectId: 1,
                                  dateReceived: 1,
                                  newLeadDate: 1,
                                  oppDate: 1,
                                  jobStartedDate: 1,
                                  checkpointActivity: 1,
                                  createdAt: 1,
                                  nextAction: 1,
                                  workingCrew: 1,
                                  warrantyType: 1,
                                  status: 1,
                                  jobCompletedDate: 1,
                              },
                          },
                          {
                              $lookup: {
                                  from: "CrmStage",
                                  localField: "stage",
                                  foreignField: "_id",
                                  as: "stageData",
                                  pipeline: [
                                      {
                                          $project: {
                                              _id: 1,
                                              name: 1,
                                              code: 1,
                                              stageGroup: 1,
                                              agingCheckpointId: 1,
                                          },
                                      },
                                  ],
                              },
                          },
                          {
                              $lookup: {
                                  from: "CrmCheckpoint",
                                  localField: "stageData.agingCheckpointId",
                                  foreignField: "_id",
                                  as: "checkPointData",
                                  pipeline: [{ $project: { _id: 1, symbol: 1 } }],
                              },
                          },
                          {
                              $lookup: {
                                  from: "ProjectType",
                                  localField: "oppType",
                                  foreignField: "_id",
                                  as: "opp-type",
                                  pipeline: [
                                      { $project: { _id: 1, name: 1, typeReplacement: 1, colorCode: 1 } },
                                  ],
                              },
                          },
                          {
                              $match: query,
                          },
                          {
                              $lookup: {
                                  from: "Contact",
                                  localField: "contactId",
                                  foreignField: "_id",
                                  as: "contact",
                                  pipeline: [{ $project: { fullName: 1, businessName: 1, isBusiness: 1 } }], // Updated fields
                              },
                          },
                          {
                              $lookup: {
                                  from: "Member",
                                  localField: "salesPerson",
                                  foreignField: "_id",
                                  as: "salesPersonData",
                                  pipeline: [{ $project: { name: 1 } }],
                              },
                          },
                          {
                              $lookup: {
                                  from: "Member",
                                  localField: "projectManager",
                                  foreignField: "_id",
                                  as: "projectManagerData",
                                  pipeline: [{ $project: { name: 1 } }],
                              },
                          },
                          {
                              $lookup: {
                                  from: "Order",
                                  localField: "orderId",
                                  pipeline: [{ $project: { _id: 1, priceTotals: 1 } }],
                                  foreignField: "_id",
                                  as: "orderData",
                              },
                          },
                          {
                              $lookup: {
                                  from: "ProjectType",
                                  localField: "acceptedType",
                                  foreignField: "_id",
                                  as: "order-type",
                                  pipeline: [{ $project: { name: 1, typeReplacement: 1, colorCode: 1 } }],
                              },
                          },
                          {
                              $lookup: {
                                  from: "Project",
                                  localField: "acceptedProjectId",
                                  pipeline: [
                                      {
                                          $project: {
                                              _id: 1,
                                              "customData.reroofAreas": 1,
                                              "customData.pitch": 1,
                                          },
                                      },
                                  ],
                                  foreignField: "_id",
                                  as: "projectData",
                              },
                          },
                          // Stage 1: Extract the activity created date safely
                          {
                              $addFields: {
                                  symbol: {
                                      $cond: [
                                          { $isArray: "$checkPointData.symbol" },
                                          { $arrayElemAt: ["$checkPointData.symbol", 0] }, // Take first element if array
                                          "$checkPointData.symbol", // Use as-is if not array
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  activityEntry: {
                                      $cond: [
                                          {
                                              $and: [
                                                  { $ne: ["$symbol", null] },
                                                  {
                                                      $eq: [{ $type: "$symbol" }, "string"],
                                                  }, // Ensure it's a string
                                              ],
                                          },
                                          {
                                              $getField: {
                                                  input: "$checkpointActivity",
                                                  field: "$symbol",
                                              },
                                          },
                                          null,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  activityCreated: {
                                      $cond: [
                                          { $ne: ["$activityEntry", null] },
                                          { $toDate: "$activityEntry.created" },
                                          null,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  daysDiff: {
                                      $cond: [
                                          { $ne: ["$activityCreated", null] },
                                          {
                                              $add: [
                                                  {
                                                      $dateDiff: {
                                                          startDate: "$activityCreated",
                                                          endDate: new Date(),
                                                          unit: "day",
                                                      },
                                                  },
                                                  1, // Include both start and end dates like the old logic
                                              ],
                                          },
                                          0,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  dateRange: {
                                      $cond: [
                                          {
                                              $and: [
                                                  { $ne: ["$daysDiff", 0] },
                                                  { $ne: ["$activityCreated", null] },
                                              ],
                                          },
                                          {
                                              $map: {
                                                  input: { $range: [0, "$daysDiff"] },
                                                  as: "dayOffset",
                                                  in: {
                                                      $dateAdd: {
                                                          startDate: "$activityCreated",
                                                          unit: "day",
                                                          amount: "$$dayOffset",
                                                      },
                                                  },
                                              },
                                          },
                                          [],
                                      ],
                                  },
                              },
                          },
                          // Stage 2: Calculate working days directly (matching old logic)
                          {
                              $addFields: {
                                  agingVal: {
                                      $cond: [
                                          { $ne: ["$activityCreated", null] },
                                          {
                                              $let: {
                                                  vars: {
                                                      // Generate array of dates from start to end
                                                      dateRange: {
                                                          $map: {
                                                              input: { $range: [0, "$daysDiff"] },
                                                              as: "dayOffset",
                                                              in: {
                                                                  $dateAdd: {
                                                                      startDate: "$activityCreated",
                                                                      unit: "day",
                                                                      amount: "$$dayOffset",
                                                                  },
                                                              },
                                                          },
                                                      },
                                                  },
                                                  in: {
                                                      // Count working days by filtering out weekend days
                                                      $size: {
                                                          $filter: {
                                                              input: "$dateRange",
                                                              as: "currentDate",
                                                              cond: {
                                                                  $not: {
                                                                      $in: [
                                                                          {
                                                                              $dayOfWeek: "$$currentDate",
                                                                          }, // 1=Sunday, 2=Monday, ..., 7=Saturday
                                                                          weekEndDayNumbers, // You need to define this array based on your weekEndDays
                                                                      ],
                                                                  },
                                                              },
                                                          },
                                                      },
                                                  },
                                              },
                                          },
                                          null,
                                      ],
                                  },
                              },
                          },
                          {
                              $project: {
                                  _id: 1,
                                  PO: 1,
                                  companyId: 1,
                                  oppType: 1,
                                  num: 1,
                                  firstName: 1,
                                  lastName: 1,
                                  street: 1,
                                  city: 1,
                                  state: 1,
                                  stage: 1,
                                  stageData: 1,
                                  checkPointData: { $arrayElemAt: ["$checkPointData", 0] },
                                  oppNotes: 1,
                                  jobNote: 1,
                                  salesPerson: 1,
                                  salesPersonName: { $arrayElemAt: ["$salesPersonData.name", 0] },
                                  projectManagerName: { $arrayElemAt: ["$projectManagerData.name", 1] },
                                  projectManager: 1,
                                  contactName: { $arrayElemAt: ["$contact.fullName", 0] },
                                  //   contactBusinessName: { $arrayElemAt: ["$contact.businessName", 0] },
                                  //   contactIsBusiness: { $arrayElemAt: ["$contact.isBusiness", 0] },
                                  opportunityId: 1,
                                  dateReceived: 1,
                                  newLeadDate: 1,
                                  oppDate: 1,
                                  jobStartedDate: 1,
                                  checkpointActivity: 1,
                                  createdAt: 1,
                                  nextAction: 1,
                                  workingCrew: 1,
                                  warrantyType: 1,
                                  orderData: { $arrayElemAt: ["$orderData", 0] },
                                  projectData: { $arrayElemAt: ["$projectData", 0] },
                                  "opp-type": { $arrayElemAt: ["$opp-type", 0] },
                                  "order-type": { $arrayElemAt: ["$order-type", 0] },
                                  agingVal: 1,
                              },
                          },
                      ]
                    : [
                          {
                              $project: {
                                  _id: 1,
                                  companyId: 1,
                                  deleted: 1,
                                  PO: 1,
                                  oppType: 1,
                                  num: 1,
                                  firstName: 1,
                                  lastName: 1,
                                  stage: 1,
                                  salesPerson: 1,
                                  contactId: 1,
                                  createdBy: 1,
                                  opportunityId: 1,
                                  dateReceived: 1,
                                  newLeadDate: 1,
                                  oppDate: 1,
                                  checkpointActivity: 1,
                                  createdAt: 1,
                                  nextAction: 1,
                                  status: 1,
                                  workingCrew: 1,
                                  projectManager: 1,
                                  jobCompletedDate: 1,
                              },
                          },
                          {
                              $lookup: {
                                  from: "CrmStage",
                                  localField: "stage",
                                  foreignField: "_id",
                                  as: "stageData",
                                  pipeline: [
                                      {
                                          $project: {
                                              _id: 1,
                                              name: 1,
                                              code: 1,
                                              stageGroup: 1,
                                              agingCheckpointId: 1,
                                          },
                                      },
                                  ],
                              },
                          },
                          {
                              $lookup: {
                                  from: "CrmCheckpoint",
                                  localField: "stageData.agingCheckpointId",
                                  foreignField: "_id",
                                  as: "checkPointData",
                                  pipeline: [{ $project: { _id: 1, symbol: 1 } }],
                              },
                          },
                          {
                              $lookup: {
                                  from: "ProjectType",
                                  localField: "oppType",
                                  foreignField: "_id",
                                  as: "opp-type",
                                  pipeline: [
                                      { $project: { _id: 1, name: 1, typeReplacement: 1, colorCode: 1 } },
                                  ],
                              },
                          },
                          {
                              $match: query,
                          },
                          {
                              $lookup: {
                                  from: "Contact",
                                  localField: "contactId",
                                  foreignField: "_id",
                                  as: "contact",
                                  pipeline: [{ $project: { fullName: 1, businessName: 1, isBusiness: 1 } }], // Updated fields
                              },
                          },
                          {
                              $unwind: {
                                  path: "$contact",
                                  preserveNullAndEmptyArrays: true,
                              },
                          },
                          {
                              $lookup: {
                                  from: "Member",
                                  localField: "salesPerson",
                                  foreignField: "_id",
                                  as: "salesPersonData",
                                  pipeline: [{ $project: { name: 1 } }],
                              },
                          },
                          {
                              $lookup: {
                                  from: "ProjectType",
                                  localField: "acceptedType",
                                  foreignField: "_id",
                                  as: "order-type",
                                  pipeline: [{ $project: { name: 1, typeReplacement: 1, colorCode: 1 } }],
                              },
                          },
                          // Stage 1: Extract the activity created date safely
                          {
                              $addFields: {
                                  symbol: {
                                      $cond: [
                                          { $isArray: "$checkPointData.symbol" },
                                          { $arrayElemAt: ["$checkPointData.symbol", 0] }, // Take first element if array
                                          "$checkPointData.symbol", // Use as-is if not array
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  activityEntry: {
                                      $cond: [
                                          {
                                              $and: [
                                                  { $ne: ["$symbol", null] },
                                                  {
                                                      $eq: [{ $type: "$symbol" }, "string"],
                                                  }, // Ensure it's a string
                                              ],
                                          },
                                          {
                                              $getField: {
                                                  input: "$checkpointActivity",
                                                  field: "$symbol",
                                              },
                                          },
                                          null,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  activityCreated: {
                                      $cond: [
                                          { $ne: ["$activityEntry", null] },
                                          { $toDate: "$activityEntry.created" },
                                          null,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  daysDiff: {
                                      $cond: [
                                          { $ne: ["$activityCreated", null] },
                                          {
                                              $add: [
                                                  {
                                                      $dateDiff: {
                                                          startDate: "$activityCreated",
                                                          endDate: new Date(),
                                                          unit: "day",
                                                      },
                                                  },
                                                  1, // Include both start and end dates like the old logic
                                              ],
                                          },
                                          0,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  dateRange: {
                                      $cond: [
                                          {
                                              $and: [
                                                  { $ne: ["$daysDiff", 0] },
                                                  { $ne: ["$activityCreated", null] },
                                              ],
                                          },
                                          {
                                              $map: {
                                                  input: { $range: [0, "$daysDiff"] },
                                                  as: "dayOffset",
                                                  in: {
                                                      $dateAdd: {
                                                          startDate: "$activityCreated",
                                                          unit: "day",
                                                          amount: "$$dayOffset",
                                                      },
                                                  },
                                              },
                                          },
                                          [],
                                      ],
                                  },
                              },
                          },
                          // Stage 2: Calculate working days directly (matching old logic)
                          {
                              $addFields: {
                                  agingVal: {
                                      $cond: [
                                          { $ne: ["$activityCreated", null] },
                                          {
                                              $let: {
                                                  vars: {
                                                      // Generate array of dates from start to end
                                                      dateRange: {
                                                          $map: {
                                                              input: { $range: [0, "$daysDiff"] },
                                                              as: "dayOffset",
                                                              in: {
                                                                  $dateAdd: {
                                                                      startDate: "$activityCreated",
                                                                      unit: "day",
                                                                      amount: "$$dayOffset",
                                                                  },
                                                              },
                                                          },
                                                      },
                                                  },
                                                  in: {
                                                      // Count working days by filtering out weekend days
                                                      $size: {
                                                          $filter: {
                                                              input: "$dateRange",
                                                              as: "currentDate",
                                                              cond: {
                                                                  $not: {
                                                                      $in: [
                                                                          {
                                                                              $dayOfWeek: "$$currentDate",
                                                                          }, // 1=Sunday, 2=Monday, ..., 7=Saturday
                                                                          weekEndDayNumbers, // You need to define this array based on your weekEndDays
                                                                      ],
                                                                  },
                                                              },
                                                          },
                                                      },
                                                  },
                                              },
                                          },
                                          null,
                                      ],
                                  },
                              },
                          },
                          {
                              $addFields: {
                                  contactName: {
                                      $cond: [
                                          { $eq: ["$contact.isBusiness", true] },
                                          "$contact.businessName",
                                          "$contact.fullName",
                                      ],
                                  },
                              },
                          },
                          {
                              $project: {
                                  _id: 1,
                                  PO: 1,
                                  oppType: 1,
                                  num: 1,
                                  firstName: 1,
                                  lastName: 1,
                                  stage: 1,
                                  stageData: 1,
                                  checkPointData: { $arrayElemAt: ["$checkPointData", 0] },
                                  salesPerson: 1,
                                  salesPersonName: { $arrayElemAt: ["$salesPersonData.name", 0] },
                                  contactName: 1,
                                  opportunityId: 1,
                                  dateReceived: 1,
                                  newLeadDate: 1,
                                  oppDate: 1,
                                  checkpointActivity: 1,
                                  createdAt: 1,
                                  nextAction: 1,
                                  "opp-type": { $arrayElemAt: ["$opp-type", 0] },
                                  "order-type": { $arrayElemAt: ["$order-type", 0] },
                                  agingVal: 1,
                              },
                          },
                      ];

            const opps = await this.opportunityModel.aggregate(pipeline);

            let opportunity = [];
            if (stageGroup === StageGroupEnum.Operations && opps.length) {
                const dailyOH = variables.dailyOH;

                opportunity = opps.map((o) => {
                    // dayCalc
                    const dayCalc = roundTo1((o.orderData?.priceTotals.overhead * 1.3) / dailyOH);

                    // jobInfo
                    const pitches = [];
                    let install = 0;
                    let remove = 0;

                    if (o.projectData?.customData?.reroofAreas) {
                        o.projectData?.customData?.reroofAreas?.forEach((area) => {
                            pitches.push(area.pitch);
                            install += area.install;
                            remove += area.remove;
                        });
                    } else {
                        pitches.push(o.projectData?.customData?.pitch);
                    }

                    const pitch = pitches.join(", ");

                    delete o.projectData;
                    delete o.orderData;
                    return {
                        ...o,
                        dayCalc,
                        jobInfo: { pitch, install: roundTo1(install), remove: roundTo1(remove) },
                    };
                });
            } else {
                opportunity = opps;
            }

            return new OkResponse({ opportunity });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOpportunityById(
        memberId: any,
        companyId: string,
        opportunityId: string,
        deleted: boolean,
        isSalesPerson: boolean,
    ) {
        try {
            const opportunity = (
                await this.opportunityModel.aggregate([
                    {
                        $match: {
                            _id: opportunityId,
                            companyId,
                            deleted,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            localField: "contactId",
                            foreignField: "_id",
                            as: "contact",
                            pipeline: [
                                {
                                    $project: {
                                        fullName: 1,
                                        firstName: 1,
                                        lastName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                        email: 1,
                                        phone: 1,
                                        fullAddress: 1,
                                        street: 1,
                                        city: 1,
                                        state: 1,
                                        zip: 1,
                                        comments: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$contact",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            let: { referredBy: "$referredBy" },
                            pipeline: [
                                { $match: { $expr: { $eq: ["$_id", "$$referredBy"] } } },
                                { $project: { name: 1 } },
                            ],
                            as: "referrer",
                        },
                    },
                    {
                        $unwind: {
                            path: "$referrer",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            let: { salesPerson: "$salesPerson", projectManager: "$projectManager" },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $in: ["$_id", ["$$salesPerson", "$$projectManager"]] },
                                    },
                                },
                                { $project: { name: 1 } },
                            ],
                            as: "members",
                        },
                    },
                    {
                        $addFields: {
                            salesPersonName: {
                                $arrayElemAt: [
                                    {
                                        $map: {
                                            input: {
                                                $filter: {
                                                    input: "$members",
                                                    as: "member",
                                                    cond: { $eq: ["$$member._id", "$salesPerson"] },
                                                },
                                            },
                                            as: "m",
                                            in: "$$m.name",
                                        },
                                    },
                                    0,
                                ],
                            },
                            projectManagerName: {
                                $arrayElemAt: [
                                    {
                                        $map: {
                                            input: {
                                                $filter: {
                                                    input: "$members",
                                                    as: "member",
                                                    cond: { $eq: ["$$member._id", "$projectManager"] },
                                                },
                                            },
                                            as: "m",
                                            in: "$$m.name",
                                        },
                                    },
                                    0,
                                ],
                            },
                        },
                    },
                    {
                        $project: {
                            activities: 0,
                            actions: 0,
                            members: 0,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            let: {
                                commentsCreatedBy: "$comments.createdBy",
                                contactCommentsCreatedBy: "$contact.comments.createdBy",
                            },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $in: [
                                                "$_id",
                                                {
                                                    $setUnion: [
                                                        "$$commentsCreatedBy",
                                                        "$$contactCommentsCreatedBy",
                                                    ],
                                                },
                                            ],
                                        },
                                    },
                                },
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                    },
                                },
                            ],
                            as: "users",
                        },
                    },
                ])
            )[0];

            if (isSalesPerson && memberId !== opportunity?.salesPerson) {
                throw new HttpException("This opportunity does not belong to you", HttpStatus.BAD_REQUEST);
            }

            const usersMap = new Map(opportunity.users.map((user) => [user._id, user]));
            // Modify comments for name
            if (opportunity && opportunity.comments && opportunity.comments.length > 0) {
                opportunity.comments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                    c.PO = opportunity.PO;
                    c.num = opportunity.num;
                    c.oppId = opportunity._id ?? opportunity.oppId;
                });
            }

            if (opportunity.contact.comments && opportunity.contact.comments.length > 0) {
                opportunity.contact.comments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                    c.contactId = opportunity.contactId;
                    opportunity.comments.push(c);
                });
            }

            return new OkResponse({ opportunity });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateChecklist(userId: string, updateChecklistDto: UpdateChecklistDto) {
        try {
            //TODO: new logic needed for place in line step as all will be id's
            // let placeInLine = undefined;
            // if (updateChecklistDto.key === "place-in-line-result") placeInLine = updateChecklistDto.value;
            const updateObj = {
                boolean: updateChecklistDto.boolean,
                value: updateChecklistDto?.value,
            };
            const result = await this.opportunityModel.updateOne(
                { _id: updateChecklistDto.opportunityId, deleted: false },
                {
                    $set: {
                        [`stepsChecklist.${updateChecklistDto.stage}.${updateChecklistDto.key}`]: updateObj,
                        // placeInLine,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            // removing step from action on uncheck
            if (!updateChecklistDto.boolean)
                this.deleteAction(updateChecklistDto.opportunityId, updateChecklistDto.key);

            return new OkResponse({ message: "Opportunity checklist updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async completeStage(companyId: string, updateCompleteStageDto: UpdateCompleteStageDto) {
        try {
            const opportunity: any = await this.opportunityModel
                .findOne({
                    companyId,
                    _id: updateCompleteStageDto.opportunityId,
                    deleted: false,
                })
                .exec();
            if (!opportunity) throw new HttpException("Opportunity not found", HttpStatus.BAD_REQUEST);
            const { [updateCompleteStageDto.stageCompleted]: stageData = {} } = opportunity;
            const result = await this.opportunityModel.updateOne(
                { _id: updateCompleteStageDto.opportunityId, deleted: false },
                {
                    [updateCompleteStageDto.stageCompleted]: {
                        ...stageData,
                        completedAt: new Date(updateCompleteStageDto.currDate),
                        completedBy: updateCompleteStageDto.completedBy,
                    },
                    stage: updateCompleteStageDto.newStage,
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            const body = `${updateCompleteStageDto.stageCompleted} stage completed`;
            this.updateOpportunityActivity(companyId, {
                memberId: updateCompleteStageDto.completedBy,
                id: updateCompleteStageDto.opportunityId,
                body,
                currDate: updateCompleteStageDto.currDate,
            });
            return new OkResponse({ message: "Opportunity stage completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateOpportunityDate(companyId: string, updateOpportunityDateDto: UpdateOpportunityDateDto) {
        try {
            const opportunity = await this.opportunityModel
                .exists({
                    companyId,
                    _id: updateOpportunityDateDto.opportunityId,
                    deleted: false,
                })
                .exec();
            if (!opportunity) throw new HttpException("Opportunity not found", HttpStatus.BAD_REQUEST);
            const newDate = updateOpportunityDateDto.date ? new Date(updateOpportunityDateDto.date) : "";
            const result = await this.opportunityModel.updateOne(
                { _id: updateOpportunityDateDto.opportunityId, deleted: false },
                {
                    [updateOpportunityDateDto.dateLabel]: newDate,
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            const body = updateOpportunityDateDto.date
                ? `${updateOpportunityDateDto.dateLabel} set to ${shortenDateTime(
                      new Date(updateOpportunityDateDto.date),
                  )}`
                : `${updateOpportunityDateDto.dateLabel} deleted`;
            this.updateOpportunityActivity(companyId, {
                memberId: updateOpportunityDateDto.updatedBy,
                id: updateOpportunityDateDto.opportunityId,
                body,
                currDate: updateOpportunityDateDto.currDate,
            });
            return new OkResponse({ message: "Opportunity date changed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async lostOpp(companyId: string, lostUnLostOpportunityDto: LostUnLostOpportunityDto) {
        try {
            const { id, date, reason, memberId } = lostUnLostOpportunityDto;

            // Fetch checkpoints
            const checkpoints = await this.crmCheckpointModel.find({ companyId }).select("symbol name");
            const symbolNames = checkpoints.map((c) => c.symbol).join(" ");

            // Fetch opportunity with selected symbols
            const opp = await this.opportunityModel
                .findOne({ _id: id, companyId })
                .select(`${symbolNames} saleDate`);
            if (!opp) throw new HttpException("Opportunity not found!", HttpStatus.BAD_REQUEST);

            if (opp?.saleDate) {
                throw new HttpException("Opportunity is already sold", HttpStatus.BAD_REQUEST);
            }

            // Prepare unset data
            let rmvCheckName = "";
            const deleteCheckpoint: any = {};
            const unsetData = checkpoints.reduce((acc, { symbol, name }) => {
                if (opp[symbol] && new Date(opp[symbol]) > new Date(date)) {
                    acc[symbol] = 1;
                    deleteCheckpoint[`checkpointActivity.${symbol}.deleted`] = new Date(date).toISOString();
                    rmvCheckName += ` & ${name} date ${formatDate(opp[symbol])} removed`;
                }
                return acc;
            }, {} as Record<string, number>);

            // Update opportunity
            const updateResult = await this.opportunityModel.updateOne(
                { _id: id, companyId },
                {
                    $set: {
                        lostReason: reason,
                        status: OpportunityStatusEnum.Lost,
                        lostDate: date,
                        lostBy: memberId,
                        "checkpointActivity.lostDate": {
                            created: new Date(date).toISOString(),
                        },
                        ...deleteCheckpoint,
                    },
                    $unset: unsetData,
                },
            );

            if (updateResult.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            const activityBody = Object.keys(unsetData).length ? reason + rmvCheckName : reason;

            // Update opportunity activity
            this.updateOpportunityActivity(companyId, {
                memberId,
                id,
                body: `Lost the Opportunity -> ${activityBody} `,
                currDate: date,
            });

            return new OkResponse({ message: "Opportunity lost status set to true" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async unLostOpp(companyId: string, lostUnLostOpportunityDto: LostUnLostOpportunityDto) {
        try {
            const result = await this.opportunityModel.updateOne(
                { _id: lostUnLostOpportunityDto.id, companyId },
                {
                    $unset: { lostDate: 1, lostBy: 1, lostReason: 1 },
                    $set: {
                        unLostReason: lostUnLostOpportunityDto.reason,
                        status: OpportunityStatusEnum.Active,
                        unLostDate: lostUnLostOpportunityDto.date,
                        unLostBy: lostUnLostOpportunityDto.memberId,
                        "checkpointActivity.unLostDate": {
                            created: new Date(lostUnLostOpportunityDto.date).toISOString(),
                            // memberId: lostUnLostOpportunityDto.memberId,
                        },

                        // `${new Date(lostUnLostOpportunityDto.date).toISOString()}` +
                        // "@" +
                        // `${lostUnLostOpportunityDto.memberId}`,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }
            this.updateOpportunityActivity(companyId, {
                memberId: lostUnLostOpportunityDto.memberId,
                id: lostUnLostOpportunityDto.id,
                body: `Un-Lost the Opportunity -> ${lostUnLostOpportunityDto.reason}`,
                currDate: lostUnLostOpportunityDto.date,
            });

            return new OkResponse({ message: "Opportunity lost status set to false" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async oppChangeStatus(companyId: string, loginMemberId: string, updateOppStatusDto: UpdateOppStatusDto) {
        try {
            const result = await this.opportunityModel.updateOne(
                {
                    _id: updateOppStatusDto.opportunityId,
                    companyId,
                    saleDate: { $exists: false },
                },
                {
                    $set: {
                        status: updateOppStatusDto.status,
                    },
                    $push: {
                        statusChanges: {
                            status: updateOppStatusDto.status,
                            statusChangedDate: new Date(),
                            statusChangedBy: loginMemberId,
                        },
                    },
                },
                // { new: true },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Opportunity status changed successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteOpportunity(companyId: string, deleteOpportunityDto: DeleteOpportunityDto) {
        try {
            // check befor delete opp
            const projects = await this.projectModel.find({
                companyId,
                oppId: deleteOpportunityDto.id,
                deleted: false,
            });
            if (projects.length > 0)
                return new UpdateFailedResponse({
                    message: "Can't delete Opportunity! Contains active Projects",
                });

            const result = await this.opportunityModel.updateOne(
                { _id: deleteOpportunityDto.id, companyId },
                {
                    $set: {
                        deleted: true,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                return new UpdateFailedResponse({ message: "Failed to update changes!" });
            }

            return new NoContentResponse({ message: "Opportunity deleted successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteOpportunity(companyId: string, permDeleteOpportunityDto: PermDeleteOpportunityDto) {
        try {
            const result = await this.opportunityModel.deleteOne({
                _id: permDeleteOpportunityDto.id,
                companyId,
            });

            if (result.deletedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "Opportunity deleted successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreOpportunity(id: string, companyId: string) {
        try {
            const opp = await this.opportunityModel.exists({ _id: id, companyId, deleted: true });
            if (!opp) throw new NotFoundException("Deleted opportunity not found");

            const { modifiedCount } = await this.opportunityModel.updateOne(
                { _id: id, companyId },
                { $set: { deleted: false } },
            );
            if (modifiedCount === 0) {
                return new UpdateFailedResponse({ message: "Failed to update changes!" });
            } else return new OkResponse({ message: "Opportunity restored successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateOpportunityActivity(companyId: string, updateOppActivityDto: UpdateOppActivityDto) {
        try {
            const activity = await this.activityModel.updateOne(
                { moduleId: updateOppActivityDto.id, companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: updateOppActivityDto.body,
                            createdBy: updateOppActivityDto.memberId,
                            createdAt: updateOppActivityDto.currDate,
                        },
                    },
                },
                { upsert: true, new: true },
            );

            // if (result.modifiedCount === 0) {
            //     return new OkResponse({ message: "Failed to update changes!" });
            // }

            return new OkResponse({ data: activity, message: "Opportunity activity updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createNewAction(companyId: string, createNewActionDto: CreateNewActionDto) {
        try {
            const result = await this.opportunityModel.updateOne(
                { _id: createNewActionDto.oppId, companyId },
                {
                    $set: {
                        nextAction: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            createdBy: createNewActionDto.memberId,
                            createdAt: createNewActionDto.currDate,
                            assignTo: createNewActionDto.assignTo,
                        },
                        salesPerson: createNewActionDto.assignTo,
                        todoCheck: false,
                    },
                    $addToSet: {
                        salesPersonHistory: createNewActionDto.assignTo,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Action created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOpportunityActivity(oppId: string) {
        try {
            const allData = await this.activityModel.aggregate([
                { $match: { moduleId: oppId } },
                {
                    $lookup: {
                        from: "Member",
                        localField: "activities.createdBy",
                        foreignField: "_id",
                        as: "userDetails",
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyId: 1,
                        oppId: 1,
                        activities: {
                            $map: {
                                input: "$activities",
                                as: "act",
                                in: {
                                    $mergeObjects: [
                                        "$$act",
                                        {
                                            name: {
                                                $let: {
                                                    vars: {
                                                        user: {
                                                            $arrayElemAt: [
                                                                {
                                                                    $filter: {
                                                                        input: "$userDetails",
                                                                        as: "user",
                                                                        cond: {
                                                                            $eq: [
                                                                                "$$user._id",
                                                                                "$$act.createdBy",
                                                                            ],
                                                                        },
                                                                    },
                                                                },
                                                                0,
                                                            ],
                                                        },
                                                    },
                                                    in: {
                                                        $ifNull: ["$$user.name", "$$act.createdBy"],
                                                    },
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                        createdAt: 1,
                        updatedAt: 1,
                    },
                },
            ]);

            if (!allData.length) {
                return new OkResponse({ oppActivities: [] });
            }

            return new OkResponse({ oppActivities: allData[0].activities });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async completeAction(companyId: string, createNewActionDto: CreateNewActionDto) {
        try {
            // If action type is None then don't count in actions
            if (createNewActionDto.type === "None") return new NoContentResponse();

            const result = await this.opportunityModel.updateOne(
                {
                    _id: createNewActionDto.oppId,
                    companyId,
                    "actions._id": { $ne: createNewActionDto.id }, // Ensures ID is not already in actions
                },
                {
                    $push: {
                        actions: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            completedBy: createNewActionDto.memberId,
                            completedAt: createNewActionDto.currDate,
                            assignTo: createNewActionDto.assignTo,
                        },
                    },
                },
            );

            // If no modification was made, action already exists or oppId is invalid
            if (result.modifiedCount === 0) {
                throw new HttpException("Action already exists or update failed!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteAction(oppId: string, id: string) {
        try {
            const result = await this.opportunityModel.updateOne(
                { _id: oppId, "actions._id": id },
                { $pull: { actions: { _id: id } } },
            );
            // If no modification was made, action already exists or oppId is invalid
            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to remove action!" });
            }

            return new OkResponse({ message: "Action completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateOpportunityStage(companyId: string, memberId: string, updateOppStageDto: UpdateOppStageDto) {
        try {
            const result = await this.opportunityModel.updateOne(
                { _id: updateOppStageDto.id },
                {
                    $set: {
                        stage: updateOppStageDto.newStage,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            const body = `Stage changed to ${updateOppStageDto.newStage}`;
            this.updateOpportunityActivity(companyId, {
                memberId: memberId,
                id: updateOppStageDto.id,
                body,
                currDate: updateOppStageDto.currDate,
            });
            return new OkResponse({ message: "Opportunity stage changed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createOpportunityComment(
        companyId: string,
        createOpportunityCommentDto: CreateOpportunityCommentDto,
    ) {
        try {
            const result = await this.opportunityModel.updateOne(
                { _id: createOpportunityCommentDto.oppId, companyId },
                {
                    $push: {
                        comments: {
                            _id: randomUUID(),
                            body: createOpportunityCommentDto.body,
                            createdBy: createOpportunityCommentDto.memberId,
                            createdAt: new Date(createOpportunityCommentDto.currDate),
                            edits: [],
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Comment created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteOpportunityComment(userId: string, deleteOpportunityCommentDto: DeleteOpportunityCommentDto) {
        try {
            const opportunity = await this.opportunityModel.exists({
                _id: deleteOpportunityCommentDto.oppId,
                comments: {
                    $elemMatch: {
                        _id: deleteOpportunityCommentDto.id,
                        createdBy: deleteOpportunityCommentDto.memberId,
                    },
                },
            });
            if (!opportunity) throw new HttpException("Opportunity not found", HttpStatus.BAD_REQUEST);

            const result = await this.opportunityModel.updateOne(
                {
                    _id: deleteOpportunityCommentDto.oppId,
                    comments: {
                        $elemMatch: {
                            _id: deleteOpportunityCommentDto.id,
                            createdBy: deleteOpportunityCommentDto.memberId,
                        },
                    },
                },
                {
                    $pull: {
                        comments: {
                            _id: deleteOpportunityCommentDto.id,
                            createdBy: deleteOpportunityCommentDto.memberId,
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "Opportunity Comment deleted successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateOpportunityComment(
        companyId: string,
        updateOpportunityCommentDto: UpdateOpportunityCommentDto,
    ) {
        try {
            const { body, currDate, id, memberId, oppId } = updateOpportunityCommentDto;

            const opportunity = await this.opportunityModel
                .findOne({
                    _id: oppId,
                    companyId,
                    // comments: {
                    //     $elemMatch: {
                    //         _id: id,
                    //         createdBy: memberId,
                    //     },
                    // },
                })
                .select("comments");
            if (!opportunity) throw new HttpException("Opportunity not found", HttpStatus.BAD_REQUEST);

            const memberComment = opportunity.comments.find((c) => c._id === id);

            if (memberComment && memberComment.createdBy !== memberId)
                throw new BadRequestException("You can only edit your comments");

            if (memberComment && memberComment?.edits?.length >= 5)
                throw new BadRequestException("Maximum numbers of edit limit reached");

            if (
                memberComment &&
                Math.abs(new Date(memberComment?.createdAt).getTime() - new Date(currDate).getTime()) >
                    60 * 60 * 1000
            )
                throw new BadRequestException("You can't edit old comment");

            // const changes = getTextChange({ body }, { body: memberComment.body });

            // const edits: any[] = changes?.body
            //     ? [{ editedAt: new Date(currDate), edit: changes?.body }, ...memberComment?.edits]
            //     : [...memberComment?.edits];

            const edits: any[] =
                body !== memberComment.body
                    ? [{ editedAt: new Date(currDate), edit: memberComment.body }, ...memberComment?.edits]
                    : [...memberComment?.edits];

            const result = await this.opportunityModel.updateOne(
                {
                    _id: oppId,
                    comments: {
                        $elemMatch: {
                            _id: id,
                            createdBy: memberId,
                        },
                    },
                },
                {
                    $set: {
                        "comments.$.body": body,
                        "comments.$.edits": edits,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Opportunity comment updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addModificationCommission(
        modCommission: AddModificationCommissionDto,
        companyId: string,
        memberId: string,
        positionCheck: boolean,
    ) {
        if (!positionCheck) {
            throw new HttpException("You are unauthorized to perform this request", HttpStatus.BAD_REQUEST);
        }

        const { date, reason, amount, oppId, salesPersonId } = modCommission;

        try {
            // Validate the date
            await this.validateModificationDate(date, companyId, salesPersonId);

            const [data] = await Promise.all([
                this.commissionModificationModel.create({
                    companyId,
                    oppId,
                    salesPersonId,
                    reason,
                    amount,
                    date,
                    createdBy: memberId,
                }),
                this.activityModel.updateOne(
                    { moduleId: oppId, moduleType: "opportunity", companyId },
                    {
                        $push: {
                            activities: {
                                _id: randomUUID(),
                                body: `added a commission modification of $${amount}`,
                                createdBy: memberId,
                                createdAt: new Date().toISOString(),
                            },
                        },
                    },
                ),
            ]);

            return new OkResponse({ data: data._id, message: "Opportunity commission added successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /*
    add validation
    1. Date field
    1. Reject if current date is more than 5 days past pay date of the pay period (it comes from PaySchedule collection) being entered.
        1. ex. If today is 9/16 and I try to enter something for 8/17, (pay date of 9/10), it will reject
        2. ex. If today is 9/15 and I try to enter something for 8/17, (pay date of 9/10), it will accept
        3. ex. the deadline for changing any modification between 9/1-9/15 is 9/30 (5 days after 9/25)
            1. If adding, don’t allow adding a date that is in the past like that
            2. if editing, don’t allow changing to a date in the past like that
            3. if editing and the date is already in the past, don’t allow editing of that modification.
     */
    async validateModificationDate(modificationDate: Date, companyId: string, salesPersonId: string) {
        try {
            const currentDate = new Date();

            const compensation = await this.compensationModel
                .aggregate([
                    { $match: { memberId: salesPersonId, companyId } },
                    { $unwind: "$wageHistory" },
                    {
                        $lookup: {
                            from: "PaySchedule",
                            localField: "wageHistory.payScheduleId",
                            foreignField: "_id",
                            as: "wageHistory.paySchedule",
                        },
                    },
                    { $unwind: "$wageHistory.paySchedule" },
                    {
                        $group: {
                            _id: "$_id",
                            memberId: { $first: "$memberId" },
                            companyId: { $first: "$companyId" },
                            wageHistory: { $push: "$wageHistory" },
                        },
                    },
                ])
                .then((res) => res[0]);

            if (!compensation) {
                throw new HttpException("No matching compensation found", HttpStatus.BAD_REQUEST);
            }

            const wage: any = findCurrentWage(compensation.wageHistory, currentDate);
            const paySchedule = wage?.paySchedule;

            if (!paySchedule) {
                throw new HttpException("No pay schedule found", HttpStatus.BAD_REQUEST);
            }

            const payEnd1 = this.extractPayDay(paySchedule.payPeriodEndsOn);
            const payEnd2 = paySchedule?.payPeriodEndsOn2
                ? this.extractPayDay(paySchedule.payPeriodEndsOn2)
                : null;
            const payday1 = this.extractPayDay(paySchedule.paydayOn);
            const payday2 = paySchedule?.paydayOn2 ? this.extractPayDay(paySchedule?.paydayOn2) : null;

            // Declare the pay period variables
            let payPeriod1StartDate: number;
            let payPeriod1EndDate: number;
            let payPeriod2StartDate: number | null = null; // Only needed for TwicePerMonth
            let payPeriod2EndDate: number | null = null; // Only needed for TwicePerMonth

            const lastDay = (date: Date) => new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();

            //TODO: update it for weekly type
            // Calculate pay periods based on pay schedule type
            if (paySchedule.period === PeriodEnum.TwicePerMonth) {
                // TwicePerMonth logic
                payPeriod1EndDate = payEnd1 >= 28 ? lastDay(modificationDate) : payEnd1;
                payPeriod1StartDate = payEnd2 ? (payEnd2 >= 28 ? 1 : payEnd2 + 1) : 1;

                payPeriod2EndDate = payEnd2
                    ? payEnd2 >= 28
                        ? lastDay(modificationDate)
                        : payEnd2
                    : lastDay(modificationDate);
                payPeriod2StartDate = payEnd1 >= 28 ? 1 : payEnd1 + 1;
            } else if (paySchedule.period === PeriodEnum.EveryWeek) {
                // for week type make it 30 days past
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                if (modificationDate < thirtyDaysAgo) {
                    throw new BadRequestException("Modification Date cannot be in a past pay period.");
                } else {
                    return true;
                }

                // EveryWeek logic
                // payPeriod1EndDate = payEnd1;
                // payPeriod1StartDate = payEnd1 + 6;
            } else if (paySchedule.period === PeriodEnum.EveryOtherWeek) {
                // for week type make it 30 days past
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                if (modificationDate < thirtyDaysAgo) {
                    throw new BadRequestException("Modification Date cannot be in a past pay period.");
                } else {
                    return true;
                }
                // EveryOtherWeek logic
                // payPeriod1EndDate = payEnd1;
                // payPeriod1StartDate = payEnd1 + 13;
            } else if (paySchedule.period === PeriodEnum.OncePerMonth) {
                // for week type make it 30 days past
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                if (modificationDate < thirtyDaysAgo) {
                    throw new BadRequestException("Modification Date cannot be in a past pay period.");
                } else {
                    return true;
                }
                // OncePerMonth logic
                // payPeriod1StartDate = 1;
                // payPeriod1EndDate = lastDay(modificationDate);
            }

            const modDate = modificationDate.getDate();
            const month = modificationDate.getMonth();
            const year = modificationDate.getFullYear();

            const payPeriod = modDate >= payPeriod1StartDate && modDate <= payPeriod1EndDate ? 1 : 2;

            const endDate = payPeriod === 1 ? payPeriod1EndDate : payPeriod2EndDate;
            const payDate = payPeriod === 1 ? payday1 : payday2 ? payday2 : payday1;

            const paydayMonth = payDate && payDate < endDate ? month + 1 : month;
            const payday = new Date(year, paydayMonth, payDate || 1, 0, 0);
            const validationDate = new Date(payday);
            validationDate.setDate(payday.getDate() + 5);

            if (validationDate < currentDate) {
                throw new BadRequestException("To late to modify commission!");
            } else {
                return true;
            }
        } catch (e) {
            if (e instanceof HttpException) {
                throw e;
            }
            throw new InternalServerErrorException(e.message);
        }
    }

    private extractPayDay(payday: string | number): number {
        if (typeof payday === "number" && !isNaN(payday)) {
            return payday;
        }
        return new Date(payday).getDate();
    }

    async updateModificationCommission(
        modCommission: UpdateModificationCommissionDto,
        companyId: string,
        memberId: string,
        positionCheck: boolean,
    ) {
        if (!positionCheck) {
            throw new HttpException("You are unauthorized to perform this request", HttpStatus.BAD_REQUEST);
        }

        const { id, date, reason, amount, oppId, salesPersonId } = modCommission;

        try {
            // Validate the date
            await this.validateModificationDate(date, companyId, salesPersonId);

            const oppComm = await this.commissionModificationModel.findOne(
                { _id: id, companyId },
                { amount: 1 },
            );

            if (!oppComm) {
                throw new BadRequestException("Opportunity commission not found!");
            }
            // const currentDate = new Date(new Date().setHours(0, 0, 0, 0));
            // const commissionDate = new Date(date);

            // // Prevent updating if the commission date is earlier than today
            // if (commissionDate < currentDate) {
            //     throw new BadRequestException("You can not update past commission!");
            // }

            await this.validateModificationDate(date, companyId, salesPersonId);

            const [result] = await Promise.all([
                this.commissionModificationModel.updateOne(
                    { _id: id, companyId },
                    {
                        companyId,
                        oppId,
                        salesPersonId,
                        reason,
                        amount,
                        date,
                        createdBy: memberId,
                    },
                ),
                this.activityModel.updateOne(
                    { moduleId: oppId, moduleType: "opportunity", companyId },
                    {
                        $push: {
                            activities: {
                                _id: randomUUID(),
                                body: `updated a commission modification from $${oppComm.amount} to $${amount}`,
                                createdBy: memberId,
                                createdAt: new Date().toISOString(),
                            },
                        },
                    },
                ),
            ]);

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ data: id, message: "Opportunity commission updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getModificationCommission(oppId: string, companyId: string) {
        try {
            const oppComm = await this.commissionModificationModel.find({ oppId, companyId });

            return new OkResponse({ oppComm });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getNum(companyId: string, po: string, warrantyType: boolean) {
        const opps = await this.opportunityModel
            .find({ PO: po, companyId, deleted: { $ne: true } })
            .sort({ num: -1 })
            .exec();
        // const oppNums = [];
        // for (let i = 0; i < opps.length; i++) {
        //     const num = opps[i].num.startsWith("W") ? Number(opps[i].num.slice(1)) : Number(opps[i].num);
        //     if (!isNaN(num)) {
        //         oppNums.push(num);
        //     }
        // }
        const oppNums = opps
            .map((opp) => (opp.num.startsWith("W") ? Number(opp.num.slice(1)) : Number(opp.num)))
            .filter((num) => !isNaN(num));

        let num;
        const previous = Math.max(...oppNums);
        if (oppNums.length !== 0) {
            num = Number(previous) + 1;
        } else {
            num = 1;
        }
        if (num < 10) {
            //add leading 0
            num = "0" + num;
        }

        if (warrantyType) return "W" + num;
        else return num;
    }

    async updateOpportunityCheckpoint(companyId: string, updateDto: UpdateOpportunityCheckpointDto) {
        try {
            const checkpoint = await this.crmCheckpointModel.findById({
                _id: updateDto.checkpointId,
                companyId,
                deleted: false,
            });

            if (!checkpoint) throw new Error("Checkpoint not found");

            const result = await this.opportunityModel.findOneAndUpdate(
                { _id: updateDto.id, companyId },
                {
                    $set: {
                        updatedAt: new Date(),
                        [checkpoint.symbol]: updateDto.currDate,
                        [`checkpointActivity.${checkpoint.symbol}`]: {
                            created: new Date(updateDto.currDate).toISOString(),
                        },
                    },
                },
                { upsert: true, new: true },
            );

            // updating contact status only if sale date is filled
            if (checkpoint?.symbol === "saleDate" && result?.contactId && result?.orderId) {
                await this.contactModel.updateOne(
                    {
                        _id: result?.contactId,
                        companyId,
                        type: { $ne: ContactTypeEnum.CLIENT },
                    },
                    {
                        $set: {
                            type: ContactTypeEnum.CLIENT,
                        },
                    },
                );

                // calling zapier webhook
                // this.notifyZapierLeadSold(updateDto.currDate, contact.email);
            }

            if (!result) {
                throw new BadRequestException("Failed to update changes!");
            }

            return new OkResponse({ message: "Opportunity checkpoint updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    private async notifyZapierLeadSold(currDate: Date, email: string) {
        const zapierWebhookUrl = "https://hooks.zapier.com/hooks/catch/2884084/2fc8pp9/";
        const postData = JSON.stringify({
            event: "lead_sold",
            // leadId: lead._id,
            saleDate: currDate,
            // name: lead.name,
            email: email,
        });

        const url = new URL(zapierWebhookUrl);

        const options = {
            hostname: url.hostname,
            path: url.pathname,
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Content-Length": Buffer.byteLength(postData),
            },
        };

        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                let responseData = "";

                res.on("data", (chunk) => {
                    responseData += chunk;
                });

                res.on("end", () => {
                    console.log("Zapier notified: Lead sold", responseData);
                    resolve(responseData);
                });
            });

            req.on("error", (error) => {
                console.error("Error notifying Zapier:", error.message);
                reject(error);
            });

            req.write(postData);
            req.end();
        });
    }

    async deleteOpportunityCheckpoint(companyId: string, deleteDto: DeleteOpportunityCheckpointDto) {
        try {
            const { symbol, id, memberId, date, name } = deleteDto;
            // finding opportunity by id and companyId
            // const opportunity = await this.opportunityModel.findOne({ _id: id, companyId });
            // if (!opportunity) {
            //     throw new Error(`Opportunity not found for company ${companyId}`);
            // }

            // const { data } = await this.getCheckpointById(userId, companyId, checkpointId, false);
            // if (!data.checkpoint) throw new Error("checkpoint not found");

            const result = await this.opportunityModel.findOneAndUpdate(
                { _id: id, companyId },
                {
                    $set: {
                        [`checkpointActivity.${symbol}.deleted`]: new Date(date).toISOString(),
                    },
                    $unset: {
                        [symbol]: 1,
                    },
                },
            );

            // updating contact status only if sale date is filled
            if (symbol === "saleDate" && result?.contactId) {
                // checking for active orders for contact
                await this.contactTypeUpdate(result.contactId, companyId);

                // calling zapier webhook
                // this.notifyZapierLeadSold(updateDto.currDate, contact.email);
            }

            const body = `${name} Checkpoint Date is deleted`;
            this.updateOpportunityActivity(companyId, {
                memberId,
                id,
                body,
                currDate: new Date(date),
            });

            // delete opportunity.checkpoint[data.checkpoint.name];
            // opportunity.markModified("checkpoint");
            // await opportunity.save();

            if (!result) throw new BadRequestException("Failed to update changes!");
            else return new OkResponse({ message: `Checkpoint deleted successfully` });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async contactTypeUpdate(contactId, companyId) {
        try {
            const activeOrder = await this.opportunityModel.find(
                {
                    contactId,
                    deleted: false,
                    orderId: { $exists: true },
                    saleDate: { $exists: true },
                },
                {
                    orderId: 1,
                },
            );

            const type = activeOrder?.length > 0 ? ContactTypeEnum.CLIENT : ContactTypeEnum.PROSPECT;

            await this.contactModel.updateOne(
                {
                    _id: contactId,
                    companyId,
                },
                {
                    $set: {
                        type,
                    },
                },
            );
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getActionsHistory(companyId: string, oppId: string) {
        try {
            const data: any = await this.opportunityModel.findOne(
                {
                    _id: oppId,
                    companyId,
                },
                { actions: 1 },
            );
            const actions = data?.actions ? data?.actions : [];

            return new OkResponse({ actions });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getStepsToDisplayOnOpp(companyId: string, oppId: string) {
        try {
            const finalStep: any = {};
            const [steps, checkpoints, oppData] = await Promise.all([
                this.crmStepModel
                    .find({
                        companyId,
                        deleted: false,
                        isDisplay: true,
                    })
                    .populate("stageId", "name", "CrmStage"),
                this.crmCheckpointModel.find({
                    companyId,
                    deleted: false,
                    isDisplay: true,
                }),
                this.opportunityModel.findOne({
                    _id: oppId,
                    companyId,
                }),
                // .select("stepsChecklist"),
            ]);

            steps.map((step: any) => {
                let value = oppData?.stepsChecklist?.[step?.stageId._id]?.[step._id]?.value;
                if (value && step.fieldType === FieldTypeEnum.Currency) {
                    value = `$ ${formatNumberToCommaS(value)}`;
                }
                finalStep[step.name] =
                    value || oppData?.stepsChecklist?.[step?.stageId._id]?.[step._id]?.boolean || "";
            });

            checkpoints.map((checkpoint) => {
                finalStep[checkpoint.name] = oppData[checkpoint.symbol] ?? "";
            });

            return new OkResponse({ finalStep });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addModifiedPieceWork(companyId: string, oppId: string, addPieceWorkDto: AddPieceWorkDto) {
        try {
            const { layers, pitches } = addPieceWorkDto;

            const pushObj =
                layers && pitches
                    ? { "modPieceWork.layers": layers, "modPieceWork.pitches": pitches }
                    : layers
                    ? { "modPieceWork.layers": layers }
                    : pitches
                    ? { "modPieceWork.pitches": pitches }
                    : {};

            const result = await this.opportunityModel.updateOne(
                {
                    _id: oppId,
                    companyId,
                },
                {
                    $push: pushObj,
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({
                message: "Opportunity Piece Work updated successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addChangeOrder(companyId: string, memberId: string, oppId: string, changeOrderDto: ChangeOrderDto) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const changeDate = new Date(changeOrderDto.date);
            if (changeDate < thirtyDaysAgo) {
                throw new BadRequestException("Change Order date cannot be more than 30 days in the past.");
            }

            const opp = await this.opportunityModel
                .findOne({ _id: oppId, companyId })
                .select(
                    "salesPerson acceptedType selfGen changeOrderValue changeOrderRRValue financeFee budgetScore changeOrders",
                );
            if (!opp) throw new BadRequestException("Opportunity not found");

            opp.changeOrderValue = opp.changeOrderValue ?? 0;
            opp.changeOrderRRValue = opp.changeOrderRRValue ?? 0;

            opp.changeOrderValue += changeOrderDto.jobCost;
            opp.changeOrderRRValue += changeOrderDto.jobCost - changeOrderDto.materials;

            delete changeOrderDto.oldJobCost;
            delete changeOrderDto.oldMaterials;

            let modificationId,
                commAmount = 0;

            // activity array
            const messageArr = [
                {
                    _id: randomUUID(),
                    body: `added a change order of total ${changeOrderDto.total}`,
                    createdBy: memberId,
                    createdAt: new Date().toISOString(),
                },
            ];

            if (changeOrderDto.signedBySales || changeOrderDto.jobCost < 0) {
                modificationId = randomUUID();
                commAmount = await this.calcSalesPersonCommission(
                    companyId,
                    opp.salesPerson,
                    opp.acceptedType,
                    changeOrderDto.jobCost,
                    changeDate,
                    opp.selfGen,
                );

                // pushing in activity array
                messageArr.push({
                    _id: randomUUID(),
                    body: `added a commission modification of $${commAmount}`,
                    createdBy: memberId,
                    createdAt: new Date().toISOString(),
                });

                await this.commissionModificationModel.create(
                    [
                        {
                            _id: modificationId,
                            companyId,
                            oppId,
                            salesPersonId: opp.salesPerson,
                            reason: "Modification",
                            amount: commAmount,
                            date: changeDate,
                            createdBy: memberId,
                        },
                    ],
                    { session },
                );
            }

            const updatedOpp = await this.opportunityModel
                .findOneAndUpdate(
                    { _id: oppId, companyId },
                    {
                        $push: { changeOrders: { ...changeOrderDto, modificationId } },
                        $set: {
                            changeOrderValue: opp.changeOrderValue,
                            changeOrderRRValue: opp.changeOrderRRValue,
                        },
                    },
                    { new: true, session },
                )
                .select("changeOrders financeFee");

            if (!updatedOpp) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            let budgetScore = opp.budgetScore ?? 0;

            if (changeOrderDto.signedBySales || changeOrderDto.jobCost < 0) {
                let { totalMaterials, totalLabor, totalJobCost } = calculateTotalCosts(
                    updatedOpp.changeOrders,
                    changeOrderDto,
                );

                const { priceTotals } = await this.orderModel
                    .findOne({ oppId, companyId, deleted: false })
                    .select("priceTotals");
                const { jobTotal, mTotal, lTotal, commission } = priceTotals;

                const previousCommissions = await this.commissionModificationModel
                    .find({ oppId }, null, { session })
                    .select("amount");

                const previousCommissionSum = (previousCommissions || []).reduce(
                    (sum, record) => sum + (record.amount || 0),
                    0,
                );
                const totalCommission = previousCommissionSum + commission;

                totalMaterials += mTotal;
                totalLabor += lTotal;
                totalJobCost += jobTotal;

                budgetScore = profitScoreCalc(
                    totalJobCost,
                    totalMaterials,
                    totalLabor,
                    totalCommission,
                    updatedOpp?.financeFee,
                );
            }

            await this.opportunityModel.updateOne(
                { _id: oppId, companyId },
                { $set: { budgetScore } },
                { session },
            );

            // creating activity
            await this.activityModel.updateOne(
                { moduleId: oppId, moduleType: "opportunity", companyId },
                {
                    $push: {
                        activities: {
                            $each: messageArr,
                        },
                    },
                },
                { session },
            );

            await session.commitTransaction();
            session.endSession();

            return new OkResponse({
                message: "Change Order Added successfully!",
                data: updatedOpp.changeOrders,
            });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) throw error;
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateChangeOrder(
        companyId: string,
        memberId: string,
        oppId: string,
        changeOrderDto: ChangeOrderDto,
    ) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const changeDate = new Date(changeOrderDto.date);

            if (changeDate < thirtyDaysAgo) {
                throw new BadRequestException("Change Order date cannot be more than 30 days in the past.");
            }

            const opp = await this.opportunityModel
                .findOne({ _id: oppId, companyId })
                .select(
                    "salesPerson acceptedType selfGen changeOrderValue changeOrderRRValue soldValue budgetScore changeOrders financeFee",
                );
            if (!opp) throw new BadRequestException("Opportunity not found");

            const { deleted, jobCost, materials, num, oldJobCost, oldMaterials } = changeOrderDto;
            let budgetScore = opp?.budgetScore ?? 0;
            let message = "updated change order";
            let message2 = "updated commission modification";
            if (deleted) {
                opp.changeOrderValue = (opp.changeOrderValue ?? 0) - jobCost;
                opp.changeOrderRRValue = (opp.changeOrderRRValue ?? 0) - (jobCost - materials);

                // ✅ Delete commission related to this change order
                await this.commissionModificationModel.deleteOne(
                    { _id: changeOrderDto?.modificationId, companyId },
                    { session },
                );
                message = `deleted change order - ${changeOrderDto.name}`;
                message2 = `deleted commission modification associated with change order - ${changeOrderDto.name}`;
            } else {
                opp.changeOrderValue = (opp.changeOrderValue ?? 0) + jobCost - (oldJobCost ?? 0);
                opp.changeOrderRRValue =
                    (opp.changeOrderRRValue ?? 0) +
                    (jobCost - (oldJobCost ?? 0)) -
                    (materials - (oldMaterials ?? 0));

                if (changeOrderDto.signedBySales || jobCost < 0) {
                    const commAmount = await this.calcSalesPersonCommission(
                        companyId,
                        opp.salesPerson,
                        opp.acceptedType,
                        jobCost,
                        changeDate,
                        opp.selfGen,
                    );

                    await this.commissionModificationModel.findOneAndUpdate(
                        { _id: changeOrderDto?.modificationId },
                        {
                            companyId,
                            oppId,
                            salesPersonId: opp.salesPerson,
                            reason: "Modification",
                            amount: commAmount,
                            date: changeDate,
                            createdBy: memberId,
                        },
                        { upsert: true, session },
                    );
                    message2 = `updated commission modification to $${commAmount} associated with change order - ${changeOrderDto.name}`;
                } else {
                    await this.commissionModificationModel.deleteOne(
                        { _id: changeOrderDto?.modificationId, companyId },
                        { session },
                    );
                    // message2 = `removed commission modification associated with change order - ${changeOrderDto.name}`;
                }

                message = `updated change order - ${changeOrderDto.name}`;
            }
            const result = await this.opportunityModel.findOneAndUpdate(
                { _id: oppId, companyId, "changeOrders.num": num },
                {
                    $set: {
                        "changeOrders.$": changeOrderDto,
                        changeOrderValue: opp.changeOrderValue,
                        changeOrderRRValue: opp.changeOrderRRValue,
                    },
                },
                { new: true, session },
            );

            if (!result) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            if (changeOrderDto.signedBySales || changeOrderDto.jobCost < 0) {
                const updatedOpp = await this.opportunityModel
                    .findOne({ _id: oppId, companyId })
                    .session(session)
                    .select("changeOrders financeFee");

                let { totalMaterials, totalLabor, totalJobCost } = calculateTotalCosts(
                    updatedOpp.changeOrders,
                    changeOrderDto,
                );

                const { priceTotals } = await this.orderModel.findOne({ oppId, companyId, deleted: false });

                const previousCommissions = await this.commissionModificationModel
                    .find({ oppId })
                    .session(session)
                    .select("amount");
                const previousCommissionSum = previousCommissions.reduce(
                    (sum, record) => sum + (record.amount || 0),
                    0,
                );

                const { jobTotal, mTotal, lTotal, commission } = priceTotals;

                const totalCommission = previousCommissionSum + commission;

                totalMaterials += mTotal;
                totalLabor += lTotal;
                totalJobCost += jobTotal;

                budgetScore = profitScoreCalc(
                    totalJobCost,
                    totalMaterials,
                    totalLabor,
                    totalCommission,
                    updatedOpp.financeFee,
                );
            }
            await this.opportunityModel.findByIdAndUpdate(oppId, { budgetScore }, { session });

            // creating activity
            await this.activityModel.updateOne(
                { moduleId: oppId, moduleType: "opportunity", companyId },
                {
                    $push: {
                        activities: {
                            $each: [
                                {
                                    _id: randomUUID(),
                                    body: message,
                                    createdBy: memberId,
                                    createdAt: new Date().toISOString(),
                                },
                                {
                                    _id: randomUUID(),
                                    body: message2,
                                    createdBy: memberId,
                                    createdAt: new Date().toISOString(),
                                },
                            ],
                        },
                    },
                },
                { session },
            );

            await session.commitTransaction();
            session.endSession();

            return new OkResponse({
                message: "Change Order Updated successfully!",
                data: result.changeOrders,
            });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) throw error;
            throw new InternalServerErrorException(error.message);
        }
    }

    async oldOpportunities(companyId: string, getOldOpportunityDto: GetOldOpportunityDto) {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);
            const limit = getOldOpportunityDto.limit || 20;
            const offset = limit * (getOldOpportunityDto.skip || 0);

            const query = {
                companyId,
                deleted: false,
                jobCompletedDate: { $lte: thirtyDaysAgo },
                ...(getOldOpportunityDto.typeId && { oppType: getOldOpportunityDto.typeId }),
                ...(getOldOpportunityDto.salesPerson && { salesPerson: getOldOpportunityDto.salesPerson }),
            };

            const opps = await this.opportunityModel
                .find(query)
                .sort({ jobCompletedDate: -1 })
                .populate("oppType", "name priceColor", "ProjectType")
                .populate("contactId", "fullName isBusiness businessName", "Contact")
                .populate("orderId", "projects.colors", "Order")
                .select(
                    "contactId PO num oppType soldValue street city state zip jobCompletedDate jobStartedDate orderId",
                )
                .limit(limit)
                .skip(offset);

            return new OkResponse({ opps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOppCompletionPercent(
        companyId: string,
        memberId: string,
        permission: PermissionsEnum,
        deleted: boolean,
        stageGroup: StageGroupEnum,
        getOpportunityDto: GetOpportunityDto,
        positionSymbol?: string,
    ) {
        try {
            // To get list of members managed by logged in member
            const { members } = await this.positionService.getManagedMembersInternal(
                memberId,
                companyId,
                permission,
                positionSymbol,
            );

            const { salesPerson, projectManager, status, oppType, crew } = getOpportunityDto;
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);

            const query: any = {
                $and: [
                    {
                        $or: [
                            {
                                "stage.code": "completed",
                                jobCompletedDate: { $gte: thirtyDaysAgo },
                            },
                            { "stage.code": { $ne: "completed" } },
                        ],
                    },
                ],
                $or: [
                    { salesPerson: { $in: members } },
                    { projectManager: { $in: members } },
                    { "workingCrew.id": { $in: members } },
                ],
                companyId,
                deleted,
                ...(status !== undefined && { status }),
                ...(stageGroup !== undefined && { "stage.stageGroup": stageGroup }),
                ...(salesPerson?.length && { salesPerson: { $in: salesPerson } }),
                ...(projectManager?.length && { projectManager: { $in: projectManager } }),
                ...(oppType?.length && { oppType: { $in: oppType } }),
                ...(crew?.length && { "workingCrew.id": { $in: crew } }),
            };

            const [opportunity, allStep, allCheckpoints] = await Promise.all([
                this.opportunityModel.aggregate([
                    {
                        $lookup: {
                            from: "CrmStage",
                            localField: "stage",
                            foreignField: "_id",
                            as: "stage",
                        },
                    },
                    {
                        $unwind: {
                            path: "$stage",
                            preserveNullAndEmptyArrays: false,
                        },
                    },

                    { $match: query },
                    { $project: { stepsChecklist: 1, stage: 1, oppType: 1, state: 1 } },
                ]),
                this.crmStepModel.find({ companyId, deleted: false }),
                this.crmCheckpointModel.find({ companyId, deleted: false }),
            ]);

            const completed = opportunity.map((opp) => {
                const stage = opp.stage;
                const step = allStep
                    .filter((s) => {
                        return (
                            s.stageId === stage._id &&
                            (!s.projectTypeId.length || s.projectTypeId.includes(opp.oppType)) &&
                            (!s.location.length || s.location.includes(opp.state))
                        );
                    })
                    .map((s) => s._id);

                const checkpoints = allCheckpoints
                    .filter((c) => c.stageSet.includes(stage._id))
                    .map((c) => c._id);

                const combinedCheckList = [...step, ...checkpoints];
                const stepCompleted = (opp?.stepsChecklist && opp.stepsChecklist?.[`${stage?._id}`]) || {};

                let count = 0;
                for (const key in stepCompleted) {
                    if (combinedCheckList.includes(key) && stepCompleted[key]?.boolean === true) {
                        count++;
                    }
                }

                const percentDone = combinedCheckList.length ? (count / combinedCheckList.length) * 100 : 0;
                return {
                    percent_done: roundTo2(percentDone),
                    _id: opp._id,
                };
            });

            return new OkResponse({
                completed,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateJobNoteInOpps(oppId: string, companyId: string, note: string) {
        try {
            const result = await this.opportunityModel.updateOne(
                { _id: oppId, companyId },
                {
                    $set: {
                        jobNote: note,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({
                message: "Job Note Updated successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getOpportunityStepChecklistByStage(_id: string, stageId: string) {
        try {
            const checklistField = `stepsChecklist.${[stageId]}`;
            const selectObject = {};
            selectObject[checklistField] = 1;

            const checklist = await this.opportunityModel.findOne({ _id }).select(selectObject);

            return new OkResponse({ checklist });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * get search list of opps using search
     * @param companyId - The ID of the company making the request.
     * @param search - The text to search.
     * @returns - A promise that resolves to an HTTP response.
     */
    async oppsSearch(companyId: string, searchOppDto: SearchOpportunityDto) {
        try {
            const { projectManager, salesPerson, search } = searchOppDto;
            const matchStage: any = {
                companyId,
                deleted: false,
                ...(salesPerson && { salesPerson }),
                ...(projectManager && { projectManager }),
            };

            if (search) {
                matchStage["$or"] = [
                    { street: { $regex: search, $options: "i" } },
                    { phone: { $regex: search, $options: "i" } },
                    { email: { $regex: search, $options: "i" } },
                    { PO: { $regex: search, $options: "i" } },
                    { num: { $regex: search, $options: "i" } },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$PO", " ", "$num"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                    {
                        $expr: {
                            $regexMatch: {
                                input: { $concat: ["$PO", "-", "$num"] },
                                regex: search,
                                options: "i",
                            },
                        },
                    },
                ];
            }

            const opps1 = await this.opportunityModel.aggregate([
                {
                    $match: matchStage,
                },
                {
                    $lookup: {
                        from: "Contact",
                        foreignField: "_id",
                        localField: "contactId",
                        as: "contact",
                        pipeline: [
                            {
                                $project: {
                                    fullName: 1,
                                    phone: 1,
                                    email: 1,
                                    businessName: 1,
                                    isBusiness: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: "$contact",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "CrmStage",
                        foreignField: "_id",
                        localField: "stage",
                        as: "stage",
                    },
                },
                {
                    $unwind: {
                        path: "$stage",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        PO: 1,
                        num: 1,
                        street: 1,
                        createdAt: 1,
                        phone: "$contact.phone",
                        email: "$contact.email",
                        fullName: {
                            $cond: [
                                { $eq: ["$contact.isBusiness", true] },
                                "$contact.businessName",
                                "$contact.fullName",
                            ],
                        },
                        stageId: "$stage._id",
                        stageName: "$stage.name",
                        stageGroup: "$stage.stageGroup",
                        status: 1,
                    },
                },
                {
                    $limit: 20,
                },
                {
                    $sort: { createdAt: -1 },
                },
            ]);
            const ids = opps1.map((o) => o._id);

            const opps2 = await this.opportunityModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                        _id: { $nin: ids },
                        ...(salesPerson && { salesPerson }),
                        ...(projectManager && { projectManager }),
                    },
                },
                {
                    $lookup: {
                        from: "Contact",
                        foreignField: "_id",
                        localField: "contactId",
                        as: "contact",
                        pipeline: [
                            {
                                $project: {
                                    fullName: 1,
                                    businessName: 1,
                                    isBusiness: 1,
                                    phone: 1,
                                    email: 1,
                                    street: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    linkedContacts: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: "$contact",
                        preserveNullAndEmptyArrays: true, // Changed to true to keep opportunities even if main contact doesn't match
                    },
                },
                // {
                //     $lookup: {
                //         from: "Contact",
                //         let: { linkedContactIds: "$contact.linkedContacts.id" },
                //         pipeline: [
                //             {
                //                 $match: {
                //                     $expr: { $in: ["$_id", "$$linkedContactIds"] },
                //                 },
                //             },
                //         ],
                //         as: "linkedContacts",
                //     },
                // },
                {
                    $match: {
                        $or: [
                            // Main contact search criteria
                            { "contact.firstName": { $regex: search, $options: "i" } },
                            { "contact.lastName": { $regex: search, $options: "i" } },
                            { "contact.businessName": { $regex: search, $options: "i" } },
                            { "contact.fullName": { $regex: search, $options: "i" } },
                            { "contact.phone": { $regex: search, $options: "i" } },
                            { "contact.email": { $regex: search, $options: "i" } },
                            { "contact.street": { $regex: search, $options: "i" } },
                            // Linked contacts search criteria
                            // { "linkedContacts.firstName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.lastName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.businessName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.fullName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.phone": { $regex: search, $options: "i" } },
                            // { "linkedContacts.email": { $regex: search, $options: "i" } },
                            // { "linkedContacts.street": { $regex: search, $options: "i" } },
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "CrmStage",
                        foreignField: "_id",
                        localField: "stage",
                        as: "stage",
                    },
                },
                {
                    $unwind: {
                        path: "$stage",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        PO: 1,
                        num: 1,
                        street: 1,
                        createdAt: 1,
                        phone: "$contact.phone",
                        email: "$contact.email",
                        fullName: {
                            $cond: [
                                { $eq: ["$contact.isBusiness", true] },
                                "$contact.businessName",
                                "$contact.fullName",
                            ],
                        },
                        stageId: "$stage._id",
                        stageName: "$stage.name",
                        stageGroup: "$stage.stageGroup",
                        status: 1,
                    },
                },
                {
                    $limit: 20,
                },
                {
                    $sort: { createdAt: -1 },
                },
            ]);

            const lead = await this.leadModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted: false,
                        status: { $ne: "converted" },
                    },
                },
                {
                    $lookup: {
                        from: "Contact",
                        foreignField: "_id",
                        localField: "contactId",
                        as: "contact",
                        pipeline: [
                            {
                                $project: {
                                    fullName: 1,
                                    businessName: 1,
                                    isBusiness: 1,
                                    phone: 1,
                                    email: 1,
                                    street: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    linkedContacts: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: "$contact",
                        preserveNullAndEmptyArrays: true, // Changed to true to keep opportunities even if main contact doesn't match
                    },
                },
                // {
                //     $lookup: {
                //         from: "Contact",
                //         let: { linkedContactIds: "$contact.linkedContacts.id" },
                //         pipeline: [
                //             {
                //                 $match: {
                //                     $expr: { $in: ["$_id", "$$linkedContactIds"] },
                //                 },
                //             },
                //         ],
                //         as: "linkedContacts",
                //     },
                // },
                {
                    $match: {
                        $or: [
                            // Main contact search criteria
                            { "contact.firstName": { $regex: search, $options: "i" } },
                            { "contact.lastName": { $regex: search, $options: "i" } },
                            { "contact.businessName": { $regex: search, $options: "i" } },
                            { "contact.fullName": { $regex: search, $options: "i" } },
                            { "contact.phone": { $regex: search, $options: "i" } },
                            { "contact.email": { $regex: search, $options: "i" } },
                            { "contact.street": { $regex: search, $options: "i" } },
                            // Linked contacts search criteria
                            // { "linkedContacts.firstName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.lastName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.businessName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.fullName": { $regex: search, $options: "i" } },
                            // { "linkedContacts.phone": { $regex: search, $options: "i" } },
                            // { "linkedContacts.email": { $regex: search, $options: "i" } },
                            // { "linkedContacts.street": { $regex: search, $options: "i" } },
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "CrmStage",
                        foreignField: "_id",
                        localField: "stageId",
                        as: "stage",
                    },
                },
                {
                    $unwind: {
                        path: "$stage",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        fullName: {
                            $cond: [
                                { $eq: ["$contact.isBusiness", true] },
                                "$contact.businessName",
                                "$contact.fullName",
                            ],
                        },
                        street: "$contact.street",
                        createdAt: 1,
                        stageId: "$stage._id",
                        stageName: "$stage.name",
                        stageGroup: "$stage.stageGroup",
                        status: 1,
                        _id: "$contact._id",
                    },
                },
                {
                    $limit: 20,
                },
                {
                    $sort: { createdAt: -1 },
                },
            ]);
            const opps = [...opps1, ...opps2, ...lead];

            return new OkResponse({ opps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * api to get count of time a person is referred
     * @param userId of loged-in user
     * @param companyId - The ID of the company making the request.
     * @returns  - A promise that resolves to an HTTP response.
     */
    async referrerCount(companyId: string) {
        try {
            const referrerCount = (
                await this.opportunityModel.aggregate([
                    {
                        $match: {
                            companyId,
                        },
                    },
                    {
                        $lookup: {
                            from: "CrmStage",
                            foreignField: "_id",
                            localField: "stage",
                            as: "stageData",
                        },
                    },
                    {
                        $unwind: {
                            path: "$stageData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $group: {
                            _id: {
                                $ifNull: ["$referredBy", "Unknown"],
                            },
                            count: { $sum: 1 },
                            opportunities: {
                                $push: {
                                    _id: "$_id",
                                    PO: "$PO",
                                    num: "$num",
                                    oppDate: "$oppDate",
                                    stageGroup: "$stageData.stageGroup",
                                },
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 0,
                            referredBy: "$_id",
                            count: "$count",
                            opportunities: 1,
                        },
                    },
                    {
                        $group: {
                            _id: null,
                            referrerCounts: {
                                $push: {
                                    k: "$referredBy",
                                    v: {
                                        count: "$count",
                                        opportunities: "$opportunities",
                                    },
                                },
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 0,
                            referrerCounts: {
                                $arrayToObject: "$referrerCounts",
                            },
                        },
                    },
                    {
                        $replaceRoot: { newRoot: "$referrerCounts" },
                    },
                ])
            )[0];

            return new OkResponse({ referrerCount });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // function to calculate sales person commission
    async calcSalesPersonCommission(
        companyId: string,
        salesPersonId: string,
        acceptedType: string,
        amount: number,
        changeDate: Date,
        selfGen: boolean,
    ) {
        try {
            const memberComp = await this.compensationModel.findOne({
                companyId,
                memberId: salesPersonId,
            });
            const salesComm = await this.payModel.findOne({ companyId });
            const pType = await this.projectTypeModel.findOne({ _id: acceptedType });

            const wage = findCurrentWage(memberComp.wageHistory, changeDate);

            const salesPersoncommission = wage?.saleCommission || 0; // already in decimal no need to divide by 100
            const selfLead = wage?.selfLead || 0;
            const { typeBonus } = salesComm.sales;

            //calculation for opp sales commision
            const bonus = typeBonus[pType._id] || 0;
            const totalCommission = roundTo2(
                // if opp lead source is self gen then it will use selfLead bonus only else normal commission
                ((selfGen ? selfLead : salesPersoncommission) + bonus) * amount,
            );

            return totalCommission;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createOpportunityForm(
        companyId: string,
        createdBy: string,
        createOpportunityFormDto: CreateOpportunityFormDto,
    ) {
        try {
            const newOpportunityForm = new this.formsModel({
                companyId,
                createdBy,
                ...createOpportunityFormDto,
            });

            await newOpportunityForm.save();

            this.sendFormSubmissionEmail(
                newOpportunityForm,
                createOpportunityFormDto,
                createdBy,
                createOpportunityFormDto?.oppId,
            ).catch((error) => console.error("Email sending failed:", error));

            return new OkResponse({ newOpportunityForm });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    private async sendFormSubmissionEmail(
        newOpportunityForm,
        createOpportunityFormDto,
        memberId: string,
        oppId?: string,
    ) {
        try {
            const formSettings = await this.formBuilderModel.findOne({
                _id: newOpportunityForm.builderFormId,
            });
            let PO = "";
            if (oppId) {
                const opp = await this.opportunityModel.findOne({ _id: oppId });
                PO = opp?.PO + "-" + opp?.num;
            }

            const sender = await this.memberModel.findOne({ _id: memberId }, "email managerId");

            if (!formSettings) {
                console.error("Form settings not found");
                return;
            }

            const { name, permissions } = formSettings;
            const recipientEmails = new Set<string>();

            // Helper to normalize emails
            const normalizeEmail = (email: string) =>
                typeof email === "string" ? email.trim().toLowerCase() : null;

            if (permissions?.whoReceivesCopy?.user && sender?.email) {
                const email = normalizeEmail(sender.email);
                if (email) recipientEmails.add(email);
            }

            if (permissions?.whoReceivesCopy?.userManager && sender?.managerId) {
                const manager = await this.memberModel.findOne({ _id: sender.managerId }, "email");
                const email = normalizeEmail(manager?.email);
                if (email) recipientEmails.add(email);
            }

            if (permissions?.whoReceivesCopy?.teamMember?.length) {
                const teamMembers = await this.memberModel.find(
                    { _id: { $in: permissions.whoReceivesCopy.teamMember } },
                    "email",
                );
                teamMembers.forEach((member) => {
                    const email = normalizeEmail(member.email);
                    if (email) recipientEmails.add(email);
                });
            }

            if (permissions?.whoReceivesCopy?.otherEmail?.length) {
                permissions.whoReceivesCopy.otherEmail.forEach((email: string) => {
                    const normEmail = normalizeEmail(email);
                    if (normEmail) recipientEmails.add(normEmail);
                });
            }

            await this.mailService.sendFormSubmissionMail(
                Array.from(recipientEmails),
                name,
                createOpportunityFormDto.mediaUrl,
                PO,
            );
        } catch (error) {
            console.error("Failed to send form submission email:", error);
        }
    }

    //TODO: add companyId
    async updateOpportunityForm(_id: string, updateOpportunityFormDto: UpdateOpportunityFormDto) {
        try {
            const existingForm = await this.formsModel.findById(_id);

            if (!existingForm) {
                throw new HttpException("Opportunity Form not found!", HttpStatus.NOT_FOUND);
            }

            const updatedForm = await this.formsModel
                .findByIdAndUpdate(_id, updateOpportunityFormDto, { new: true })
                .exec();

            if (!updatedForm) {
                throw new HttpException("Failed to update changes!", HttpStatus.NOT_MODIFIED);
            }
            return new OkResponse({ updatedForm });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //TODO: add companyId
    async getOpportunityForm(formId: string) {
        try {
            const form = await this.formsModel.findOne({ _id: formId, deleted: false }).exec();
            if (!form) {
                throw new HttpException("Opportunity form not found!", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({ form });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //TODO: add companyId
    async deleteOpportunityForm(_id: string) {
        try {
            const updatedForm = await this.formsModel
                .findByIdAndUpdate(_id, { deleted: true }, { new: true })
                .exec();

            if (!updatedForm) {
                throw new HttpException("Opportunity form not found!", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({ message: "Opportunity form deleted successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //TODO: add companyId
    async getMyOpportunityForms(
        memberId: string,
        companyId: string,
        teamPermission: number,
        deleted: boolean,
    ) {
        try {
            const { members } = await this.positionService.getManagedMembersInternal(
                memberId,
                companyId,
                teamPermission,
            );

            const allowedMembers = [...members, memberId];

            const forms = await this.formsModel.aggregate([
                { $match: { createdBy: { $in: allowedMembers }, deleted } },
                {
                    $lookup: {
                        from: "Member",
                        localField: "createdBy",
                        foreignField: "_id",
                        as: "member",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                {
                    $unwind: { path: "$member", preserveNullAndEmptyArrays: true },
                },
                {
                    $lookup: {
                        from: "Opportunity",
                        localField: "oppId",
                        foreignField: "_id",
                        as: "opp",
                        pipeline: [{ $project: { PO: 1, num: 1 } }],
                    },
                },
                { $unwind: { path: "$opp", preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        memberName: "$member.name",
                        PO: "$opp.PO",
                        num: "$opp.num",
                    },
                },
                {
                    $sort: { createdAt: -1 },
                },
                {
                    $project: {
                        _id: 1,
                        oppId: 1,
                        PO: 1,
                        num: 1,
                        builderFormId: 1,
                        deleted: 1,
                        name: 1,
                        mediaId: 1,
                        mediaUrl: 1,
                        createdBy: 1,
                        memberName: 1,
                        createdAt: 1,
                    },
                },
            ]);

            return new OkResponse({ forms: forms || [] });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
