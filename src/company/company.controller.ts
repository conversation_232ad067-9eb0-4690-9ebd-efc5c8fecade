import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Put, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiConflictResponse,
    ApiInternalServerErrorResponse,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiQuery,
    ApiResponse,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Positions, Roles } from "src/auth/guards/auth.guard";
import { PositionService } from "src/position/position.service";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CompanyService } from "./company.service";
import { CreateCompanySettingDto } from "./dto/create-company-setting.dto";
import { GetCompanyMemberDto, FetchMemberDto } from "./dto/get-company-member.dto";
import { InviteMemberDto } from "./dto/invite-member.dto";
import { PaginationRequestDto } from "./dto/pagination-request.dto";
import { UpdateMemberCompanyInfoDto } from "./dto/update-member-company-info.dto";
import { UserInvitationDto } from "./dto/user-invitation.dto";
import { UserRolesEnum } from "./enum/role.enum";
import { CreateCompanyCommisionDto } from "./dto/create-company-commision.dto";
import { TerminateMemberDto } from "./dto/terminate-member.dto";
import { CreateReferrerDto } from "./dto/create-referrer.dto";
import { DeleteRestoreReferrerDto } from "./dto/delete-referrer.dto";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { UpdateContractDto, UpdateSectionDto, UpdateSectionOrderDto } from "./dto/update-contract.dto";
import { CreateContractDto, CreateSectionDto } from "./dto/create-contract.dto";
import { InvitationWageDto } from "./dto/invitation-wage-update.dto";
import { moduleNames } from "src/shared/constants/constant";
import { UpdateTagDto } from "./dto/update-company-tag.dto";
import { DeleteTagDto } from "./dto/delete-company-tag.dto";
import { CreateSalesActionCompanyDto } from "./dto/create-company-sales-action.dto";
import { UpdateSalesActionDto } from "src/crm/dto/update-sales-action.dto";
import { DeleteSalesActionDto } from "src/crm/dto/delete-sales-action.dto";
import { CreateContentBlockDto } from "./dto/create-content.dto";
import { GetContentDto } from "./dto/get-content.dto";
import { UpdateContentBlockDto } from "./dto/update-content.dto";

@ApiTags("Company")
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "company", version: "1" })
export class CompanyOpenController {
    constructor(private readonly companyService: CompanyService) {}

    /**
     * Get user invitation by invitation ID
     * @param invitationId The ID of the invitation to retrieve
     * @returns The user invitation corresponding to the given ID
     */
    @ApiOperation({ summary: "Get User Invitation By id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("user-invitations/invitation/:invitationId")
    async getUserInvitationsById(@Param("invitationId") invitationId: string) {
        return this.companyService.getUserInvitationById(invitationId);
    }

    /**
     * Get user by email
     * @param userId The ID of the user making the request.
     * @param email The email of the user to retrieve
     * @returns The user with the matching email
     */
    @ApiOperation({ summary: "Get User By Email" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-user-by-email/email/:email")
    async getUserByEmail(@Param("email") email: string) {
        return this.companyService.getUserByEmail(email);
    }

    // /**
    //  *Endpoint for a user to respond to an invitation to join a company.
    //  *@param {InvitationResponseDto} invitationResponseDto - The data needed to respond to the invitation.
    //  *@returns {Promise<HttpResponse>} - The response with the result of the operation.
    //  *@throws {ApiException} - If the invitation does not exist or if the server encounters an error.
    //  *@summary Invitation Response
    //  */
    // @ApiOperation({ summary: "Invitation Response" })
    // @ApiConflictResponse({ description: "Invitation doesnot exist" })
    // @Post("invitation-response")
    // async invitationResponse(@Body() invitationResponseDto: InvitationResponseDto): Promise<HttpResponse> {
    //     return this.companyService.invitationResponse(invitationResponseDto);
    // }
}
@ApiTags("Company")
@Auth()
@ApiBearerAuth()
@ApiInternalServerErrorResponse({ description: "Server Error" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@Controller({ path: "company", version: "1" })
export class CompanyController {
    constructor(
        private readonly companyService: CompanyService,
        private readonly positionService: PositionService,
    ) {}

    /**
     * update a new company.
     *@param {string} userId - The ID of the user creating the company.
     *@param {UpdateCompanyDto} updateCompanyDto - The data needed to update a new company.
     *@returns {Promise<HttpResponse>} - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "update Company" })
    @ApiConflictResponse({ description: "Company already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch()
    async updateCompany(
        @GetUser() user: JwtUserPayload,
        @Body() updateCompanyDto: UpdateCompanyDto,
    ): Promise<HttpResponse> {
        return this.companyService.updateCompany(user.companyId, updateCompanyDto);
    }

    /**
     *Get company invitation & member count.
     *@param {string} userId - The ID of the user creating the company.
     *@param {CreateCompanyDto} createCompanyDto - The data needed to create a new company.
     *@returns {Promise<HttpResponse>} - An HTTP response indicating success or failure.
     */
    @ApiOperation({ summary: "Create Company" })
    @ApiConflictResponse({ description: "Company already exist" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("member-count-metrics")
    async companyInvitationAndActiveUserCount(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.companyService.companyInvitationAndActiveUserCount(user.companyId);
    }

    /**
     * This API endpoint is used to invite a member to a company.
     * @param userId The ID of the user making the request.
     * @param inviteMemberDto The data transfer object containing the details of the invitation.
     * @returns A Promise resolving to an HttpResponse object.
     * @throws ApiConflictResponse if the company does not exist or the member already exists.
     * @throws ApiInternalServerErrorResponse if there is a server error.
     * @throws AuthError if the user making the request is not authenticated.
     * @throws PositionsError if the user making the request does not have the necessary permissions.
     */
    @ApiOperation({ summary: "Invite Members" })
    @ApiConflictResponse({ description: "Company doesnot exist or member already exist" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Post("invite-member")
    async inviteMember(
        @GetUser() user: JwtUserPayload,
        @Body() inviteMemberDto: InviteMemberDto,
    ): Promise<HttpResponse> {
        return this.companyService.inviteMember(user._id, user.companyId, inviteMemberDto);
    }

    /**
     * This API endpoint is used to add or update invite a member compensation.
     * @param userId The ID of the user making the request.
     * @param invitationWageDto The data transfer object containing the details of the compensation.
     * @returns A Promise resolving to an HttpResponse object.
     * @throws ApiConflictResponse if the company does not exist or the member already exists.
     * @throws ApiInternalServerErrorResponse if there is a server error.
     * @throws AuthError if the user making the request is not authenticated.
     * @throws PositionsError if the user making the request does not have the necessary permissions.
     */
    @ApiOperation({ summary: "Add or update Invite Members compensation" })
    @ApiConflictResponse({ description: "Company doesnot exist or member already exist" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("invitation-compensation/:id")
    async updateInviteMemberCompensation(
        @Param("id", ParseUUIDPipe) id: string,
        @GetUser() user: JwtUserPayload,
        @Body() invitationWageDto: InvitationWageDto,
    ): Promise<HttpResponse> {
        return this.companyService.updateInviteMemberCompensation(
            user._id,
            user.companyId,
            id,
            invitationWageDto,
        );
    }

    /**
     * Revokes the invitation sent to a member by the company
     * @param userId The id of the user making the request
     * @param userInvitationDto The DTO containing information about the user and company invitation
     * @returns A Promise that resolves to an HTTP response indicating the success or failure of the request
     */
    @ApiOperation({ summary: "Revoke Member Invitation" })
    @ApiConflictResponse({
        description: "Company doesnot exist or member already exist or inviation already accepted",
    })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full],
    })
    @Post("revoke-invitation")
    async revokeInvitation(
        @GetUser() user: JwtUserPayload,
        @Body() userInvitationDto: UserInvitationDto,
    ): Promise<HttpResponse> {
        return this.companyService.revokeInvitation(user.companyId, userInvitationDto);
    }

    /**
     * Resend Member Invitation
     * @param userId The id of the user performing the request
     * @param userInvitationDto The data of the user invitation to be resent
     * @returns A Promise of an HTTP response containing the updated user invitation data
     */
    @ApiOperation({ summary: "Resend Member Invitation" })
    @ApiConflictResponse({
        description: "Company doesnot exist or member already exist inviation already accepted",
    })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full],
    })
    @Post("resend-invitation")
    async resendInvitation(
        @GetUser() user: JwtUserPayload,
        @Body() userInvitationDto: UserInvitationDto,
    ): Promise<HttpResponse> {
        return this.companyService.resendInvitation(user._id, user.companyId, userInvitationDto);
    }

    /**
     *Endpoint to update the information of a member in a company.
     *@param {string} userId - The ID of the user making the request.
     *@param {UpdateMemberCompanyInfoDto} updateMemberCompanyInfoDto - The data needed to update the member's information in the company.
     *@returns {Promise<HttpResponse>} - The response with the result of the operation.
     *@throws {ApiException} - If the company or member do not exist or if the server encounters an error.
     *@summary Update Member Company Info
     */
    @ApiOperation({ summary: "Update Member Company Info" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("update-member-company-info")
    async updateMemberCompanyInfo(
        @GetUser() user: JwtUserPayload,
        @Body() updateMemberCompanyInfoDto: UpdateMemberCompanyInfoDto,
    ): Promise<HttpResponse> {
        return this.companyService.updateMemberCompanyInfo(
            user._id,
            user.companyId,
            updateMemberCompanyInfoDto,
            user.teamPermission,
        );
    }

    /**
     *Endpoint to get all the companies of a user.
     *@param {string} userId - The ID of the user whose companies are being fetched.
     *@returns {Promise<Array<Company>>} - The list of companies that the user is associated with.
     *@throws {ApiException} - If the user does not exist or if the server encounters an error.
     *@summary Get User Companies
     */
    @ApiOperation({ summary: "Get User Companies" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("user")
    async getUserCompanies(@GetUser() user: JwtUserPayload) {
        return this.companyService.getUserCompanies(user._id);
    }

    /**
     *Endpoint to get all the members of a company, based on the provided company ID and deleted status.
     *@param {string} userId - The ID of the user making the request.
     *@param {string} companyId - The ID of the company to get the members for.
     *@param {boolean} deleted - The deleted status of the members to retrieve.
     *@param {PaginationRequestDto} paginationRequestDto - The pagination details for the request.
     *@returns {Promise<HttpResponse>} - The response with the result of the operation.
     *@throws {ApiException} - If the user is not authorized, if the company or the user do not exist, or if the server encounters an error.
     *@summary Get Company Members
     */
    @ApiOperation({ summary: "Get Company Members" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("members/deleted/:deleted")
    async getCompanyMembers(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() paginationRequestDto: PaginationRequestDto,
    ) {
        return this.companyService.getCompanyMembers(
            user._id,
            user.companyId,
            deleted,
            paginationRequestDto,
            user.teamPermission,
        );
    }

    /**
     *Endpoint to get all the admins & owner of a company, based on the provided company ID.
     *@param {string} companyId - The ID of the company to get the members for.
     *@returns {Promise<HttpResponse>} - The response with the result of the operation.
     *@throws {ApiException} - If the user is not authorized, if the company or the user do not exist, or if the server encounters an error.
     *@summary Get Company Admins & Owners
     */
    @ApiOperation({ summary: "Get Company Admins & Owners" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Roles(UserRolesEnum.Owner)
    @Get("admins-owners")
    async getAdminsAndOwner(@GetUser() user: JwtUserPayload) {
        return this.companyService.getAdminsAndOwner(user.companyId);
    }

    /**
     *Controller to get all members of a company by a specific position.
     *@param userId The ID of the user making the request.
     *@param companyId The ID of the company to get members from.
     *@param positionId The ID of the positions to filter by.
     *@param deleted A boolean flag indicating whether to include deleted members.
     *@param paginationRequestDto An object containing pagination options.
     *@returns A list of company members matching the given position.
     *@ApiOperation provides a summary of the endpoint for API documentation.
     *@ApiUnauthorizedResponse indicates the response when the request is unauthorized.
     *@Get specifies the HTTP method and endpoint for this controller.
     */
    @ApiOperation({ summary: "Get Company Members by positions" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("members")
    async getCompanyMembersByPosition(
        @GetUser() user: JwtUserPayload,
        @Query() fetchMemberDto: FetchMemberDto,
    ) {
        return this.companyService.getCompanyMembersByPosition(
            user.memberId,
            user.companyId,
            user.teamPermission,
            fetchMemberDto,
        );
    }

    @ApiOperation({ summary: "Get Company Members by positions with 'Actions" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.actions,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("members-with-actions")
    async getCompanyMembersByPositionAndAction(
        @GetUser() user: JwtUserPayload,
        @Query() fetchMemberDto: FetchMemberDto,
    ) {
        return this.companyService.getCompanyMembersByPositionWithActions(
            user.memberId,
            user.companyId,
            user.teamPermission,
            fetchMemberDto,
        );
    }

    /**
     *Controller to get the salespersons & project managers of companyId
     *@param {string} companyId - Company Id of the member
     *@returns {Promise<Member>} - Returns a Promise that resolves to a Member object.
     *@throws {UnauthorizedException} - If the request is unauthorized.
     */
    @ApiOperation({ summary: "Get Company sales persons & Project managers" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @ApiQuery({
        name: "departmentId",
        required: false,
        type: "string",
    })
    @ApiQuery({
        name: "onlyPM",
        required: false,
        type: "boolean",
    })
    @Get("salesperson-and-pm")
    async getSalesPersonAndProjectManager(
        @GetUser() user: JwtUserPayload,
        @Query("departmentId") departmentId?: string,
        @Query("onlyPM") onlyPM?: boolean,
    ) {
        return this.companyService.getSalesPersonAndProjectManager(user.companyId, departmentId, onlyPM);
    }

    /**
     *Controller to get the member detail by user and companyId
     *@param {string} userId - User Id of the member
     *@param {string} companyId - Company Id of the member
     *@returns {Promise<Member>} - Returns a Promise that resolves to a Member object.
     *@throws {UnauthorizedException} - If the request is unauthorized.
     *@throws {NotFoundException} - If member with specified id is not found.
     *@throws {InternalServerErrorException} - If any error occurs while processing the request.
     */
    @ApiOperation({ summary: "Get Member By User and companyId" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("get-member-detail")
    async getMemberByUserId(@GetUser() user: JwtUserPayload) {
        return this.companyService.getMemberByUserId(user._id, user.companyId);
    }

    /**
     *Controller for getting member details
     *@param userId - The ID of the user making the request
     *@param companyId - The ID of the company the member belongs to
     *@param memberId - The ID of the member whose details are being retrieved
     *@returns The details of the specified member
     *@throws ApiUnauthorizedResponse if the user making the request is not authorized
     */
    @ApiOperation({ summary: "Get Member Detail" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("member/id/:memberId")
    async getMemberDetail(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ) {
        return this.companyService.getMemberDetail(user._id, user.companyId, memberId, user.teamPermission);
    }

    /**
     *GET endpoint to retrieve all invitations of a company.
     *@param {string} userId - The ID of the user making the request.
     *@param {string} companyId - The ID of the company to retrieve invitations for.
     *@param {PaginationRequestDto} paginationRequestDto - Object containing pagination data.
     *@returns {Promise<CompanyInvitationDto[]>} - An array of CompanyInvitationDto objects representing the company invitations.
     */
    @ApiOperation({ summary: "Get Company Invitations" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full],
    })
    @Get("invitations")
    async getCompanyInvitations(
        @GetUser() user: JwtUserPayload,
        @Query() paginationRequestDto: PaginationRequestDto,
    ) {
        return this.companyService.getCompanyInvitations(user._id, user.companyId, paginationRequestDto);
    }

    /**
     *Get all the direct reports of a manager by their ID.
     *@param userId The ID of the user making the request.
     *@param companyId The ID of the company to which the manager belongs.
     *@param memberId The ID of the manager whose direct reports to retrieve.
     *@returns A list of the direct reports of the specified manager.
     */
    @ApiOperation({ summary: "Get Direct Report By manager id" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("direct-report/member/:memberId")
    async getDirectReportsByManagerId(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
    ) {
        return this.companyService.getDirectReportsByManagerId(user._id, user.companyId, memberId);
    }

    /**
     * Permanently delete a member from the company
     *@param userId The ID of the user making the request
     *@param getCompanyMemberDto DTO containing the company ID and the member's ID to be deleted
     *@returns HttpResponse object with the result of the delete operation
     */
    @ApiOperation({ summary: "Delete Member" })
    @ApiConflictResponse({ description: "Member is a part of a crew" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Delete("delete-member")
    async deleteMember(
        @GetUser() user: JwtUserPayload,
        @Body() getCompanyMemberDto: GetCompanyMemberDto,
    ): Promise<HttpResponse> {
        return this.companyService.permDeleteMember(
            user._id,
            user.companyId,
            getCompanyMemberDto,
            user.teamPermission,
        );
    }

    /**
     * Terminate member by ID.
     * @param userId - The ID of the user to terminate.
     * @param getCompanyMemberDto - The DTO containing information about the member's company.
     * @returns A promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Terminate Member" })
    @ApiConflictResponse({ description: "Member is a part of a crew" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Delete("terminate-member")
    async terminateMember(
        @GetUser() user: JwtUserPayload,
        @Body() terminateMemberDto: TerminateMemberDto,
    ): Promise<HttpResponse> {
        return this.companyService.terminateMember(
            user._id,
            user.companyId,
            terminateMemberDto,
            user.teamPermission,
        );
    }

    /**
     * Restore member by ID.
     * @param userId - The ID of the user to Restore.
     * @param getCompanyMemberDto - The DTO containing information about the member's company.
     * @returns A promise that resolves to an HttpResponse object.
     */
    @ApiOperation({ summary: "Restore Member" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "module",
        name: moduleNames.module.team,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Patch("restore-member")
    async restoreMember(
        @GetUser() user: JwtUserPayload,
        @Body() getCompanyMemberDto: GetCompanyMemberDto,
    ): Promise<HttpResponse> {
        return this.companyService.restoreMember(
            user._id,
            getCompanyMemberDto,
            user.teamPermission,
            user.companyId,
        );
    }

    /**
     * Upsert a company setting
     * @param userId The ID of the user making the request
     * @param createCompanySettingDto The DTO containing the data for the company setting to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Upsert Company Setting" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("upsert-company-setting")
    async upsertCompanySetting(
        @GetUser() user: JwtUserPayload,
        @Body() createCompanySettingDto: CreateCompanySettingDto,
    ): Promise<HttpResponse> {
        return this.companyService.upsertCompanySetting(user.companyId, createCompanySettingDto);
    }

    /**
     * Get Company Setting by Company Id
     * @param userId User ID
     * @param companyId Company ID
     * @returns The company setting of the given company ID
     */
    @ApiOperation({ summary: "Get CompanySetting" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("get-company-setting")
    async getCompanySetting(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.companyService.getCompanySetting(user._id, user.companyId);
    }

    /**
     * Get Company Address & some other detail for all users
     * @param userId User ID
     * @param companyId Company ID
     * @returns The company Address & other details of the given company ID
     */
    @ApiOperation({ summary: "Get Company Address" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("company-address")
    async getCompanyAddress(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.companyService.getCompanyAddress(user._id, user.companyId);
    }

    /**
     * Upsert a company commision setting
     * @param userId The ID of the user making the request
     * @param createCompanyCommisionDto The DTO containing the data for the company commision setting to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Upsert Company commision" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("upsert-company-commision")
    async upsertCompanyCommision(
        @GetUser() user: JwtUserPayload,
        @Body() createCompanyCommisionDto: CreateCompanyCommisionDto,
    ): Promise<HttpResponse> {
        return this.companyService.upsertCompanyCommision(user._id, createCompanyCommisionDto);
    }

    /**
     * Get Company commision by Company Id
     * @param userId User ID
     * @param companyId Company ID
     * @returns The company commision of the given company ID
     */
    @ApiOperation({ summary: "Get Company commision" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Get("get-company-commision")
    async getCompanyCommision(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.companyService.getCompanyCommisionSettings(user._id, user.companyId);
    }

    /**
     *  Create Referrer
     * @param userId The ID of the user making the request
     * @param createReferrer The DTO containing the data for the referrer to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Create referrer" })
    @Post("create-referrer")
    async createReferrer(
        @GetUser() user: JwtUserPayload,
        @Body() createReferrer: CreateReferrerDto,
    ): Promise<HttpResponse> {
        return this.companyService.createReferrer(user.companyId, createReferrer);
    }

    /**
     *  Get List of all Referrer with company members
     * @param userId The ID of the user making the request
     * @param createReferrer The DTO containing the data for the referrer to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Get List of all referrer" })
    @Get("referrers/deleted/:deleted/allReferrers/:allReferrers")
    async getReferrers(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Param("allReferrers") allReferrers: boolean,
    ): Promise<HttpResponse> {
        return this.companyService.getReferrers(user._id, user.companyId, deleted, allReferrers);
    }

    /**
     * Restore Referrer
     * @param userId The ID of the user making the request
     * @param createReferrer The DTO containing the data for the referrer to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Restore referrer" })
    @Patch("restore-referrer")
    async restoreReferrer(
        @GetUser() user: JwtUserPayload,
        @Body() restoreReferrer: DeleteRestoreReferrerDto,
    ): Promise<HttpResponse> {
        return this.companyService.restoreReferrer(user.companyId, restoreReferrer);
    }

    /**
     * Delete Referrer
     * @param userId The ID of the user making the request
     * @param createReferrer The DTO containing the data for the referrer to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Delete referrer" })
    @Delete("delete-referrer")
    async deleteReferrer(
        @GetUser() user: JwtUserPayload,
        @Body() deleteReferrer: DeleteRestoreReferrerDto,
    ): Promise<HttpResponse> {
        return this.companyService.deleteReferrer(user.companyId, deleteReferrer);
    }

    /**
     * Permanent Delete Referrer
     * @param userId The ID of the user making the request
     * @param createReferrer The DTO containing the data for the referrer to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Delete referrer" })
    @Delete("perm-delete-referrer")
    async permDeleteReferrer(
        @GetUser() user: JwtUserPayload,
        @Body() deleteReferrer: DeleteRestoreReferrerDto,
    ): Promise<HttpResponse> {
        return this.companyService.permDeleteReferrer(user.companyId, deleteReferrer);
    }

    /**
     * Weather API
     * @param userId The ID of the user making the request
     * @param createReferrer The DTO containing the data for the referrer to be upserted
     * @returns The HTTP response indicating whether the operation was successful or not
     */
    @ApiOperation({ summary: "Weather Forecast API" })
    @Get("weather-forecast/zipcode/:zipcode/data/:date")
    async weatherApi(@Param("zipcode") zipcode: string, @Param("date") date: Date): Promise<HttpResponse> {
        return this.companyService.weatherApi(zipcode, date);
    }

    // @ApiOperation({ summary: "Register a Company" })
    // @ApiConflictResponse({ description: "Company already exist" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Post("register-company")
    // async registerCompany(@Body() registerCompanyDto: RegisterCompanyDto): Promise<HttpResponse> {
    //     return this.companyService.registerCompany(registerCompanyDto);
    // }

    @Post("create-contract")
    @ApiOperation({ summary: "Create a new contract" })
    @ApiResponse({ status: 201, description: "Contract created successfully." })
    @ApiResponse({ status: 400, description: "Invalid input data." })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    createContract(@GetUser() user: JwtUserPayload, @Body() createContractDto: CreateContractDto) {
        return this.companyService.createContract(user.companyId, createContractDto);
    }

    @Get("get-all-contracts")
    @ApiOperation({ summary: "Get all contracts for a company" })
    @ApiResponse({ status: 200, description: "Contracts retrieved successfully." })
    @ApiQuery({
        name: "projectType",
        required: false,
        type: String,
        description: "Optional project type UUID",
    })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    findAllContracts(@GetUser() user: JwtUserPayload, @Query("projectType") projectType?: string) {
        return this.companyService.findAllContracts(user.companyId, projectType ? [projectType] : []);
    }

    @Get("get-contract/:id")
    @ApiOperation({ summary: "Get a specific contract by ID" })
    @ApiParam({ name: "id", description: "UUID of the contract" })
    @ApiResponse({ status: 200, description: "Contract retrieved successfully." })
    @ApiResponse({ status: 404, description: "Contract not found." })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    finContract(@GetUser() user: JwtUserPayload, @Param("id", ParseUUIDPipe) id: string) {
        return this.companyService.findContract(id, user.companyId);
    }

    @Patch("update-contract/:id")
    @ApiOperation({ summary: "Update a contract by ID" })
    @ApiParam({ name: "id", description: "UUID of the contract" })
    @ApiResponse({ status: 200, description: "Contract updated successfully." })
    @ApiResponse({ status: 404, description: "Contract not found." })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    updateContract(
        @Param("id", ParseUUIDPipe) id: string,
        @GetUser() user: JwtUserPayload,
        @Body() updateContractDto: UpdateContractDto,
    ) {
        return this.companyService.updateContract(id, user.companyId, updateContractDto);
    }

    @Delete("delete-contract/:id")
    @ApiOperation({ summary: "Delete a contract by ID" })
    @ApiParam({ name: "id", description: "UUID of the contract" })
    @ApiResponse({ status: 200, description: "Contract deleted successfully." })
    @ApiResponse({ status: 404, description: "Contract not found." })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    deleteContract(@Param("id", ParseUUIDPipe) id: string, @GetUser() user: JwtUserPayload) {
        return this.companyService.deleteContract(id, user.companyId);
    }

    @Delete("delete-section/contractId/:contractId/sectionId/:sectionId")
    @ApiOperation({ summary: "Delete a section by ID" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    deleteSection(
        @Param("contractId", ParseUUIDPipe) contractId: string,
        @Param("sectionId", ParseUUIDPipe) sectionId: string,
        @GetUser() user: JwtUserPayload,
    ) {
        return this.companyService.deleteSection(sectionId, contractId, user.companyId);
    }

    @Post("add-section/contractId/:contractId")
    @ApiOperation({ summary: "Add new section to contract" })
    @ApiResponse({ status: 201, description: "Section added successfully." })
    @ApiResponse({ status: 400, description: "Invalid input data." })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    addSection(
        @GetUser() user: JwtUserPayload,
        @Param("contractId", ParseUUIDPipe) contractId: string,
        @Body() createSectionDto: CreateSectionDto,
    ) {
        return this.companyService.addSection(user.companyId, contractId, createSectionDto);
    }

    @Put("update-section/contractId/:contractId/sectionId/:sectionId")
    @ApiOperation({ summary: "Add new section to contract" })
    @ApiResponse({ status: 201, description: "Section added successfully." })
    @ApiResponse({ status: 400, description: "Invalid input data." })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    updateSection(
        @GetUser() user: JwtUserPayload,
        @Param("contractId", ParseUUIDPipe) contractId: string,
        @Param("sectionId", ParseUUIDPipe) sectionId: string,
        @Body() createSectionDto: UpdateSectionDto,
    ) {
        return this.companyService.updateSection(user.companyId, contractId, sectionId, createSectionDto);
    }

    @Get("get-sections/contractId/:contractId")
    @ApiOperation({ summary: "To fetch sections" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    getSections(@GetUser() user: JwtUserPayload, @Param("contractId", ParseUUIDPipe) contractId: string) {
        return this.companyService.getSections(user.companyId, contractId);
    }

    @Put("order-section/contractId/:contractId")
    @ApiOperation({ summary: "Section order updated successfully" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    orderSections(
        @GetUser() user: JwtUserPayload,
        @Param("contractId", ParseUUIDPipe) contractId: string,
        @Body() updateSectionOrder: UpdateSectionOrderDto,
    ) {
        return this.companyService.updateSectionOrder(user.companyId, contractId, updateSectionOrder);
    }

    @Get("contracts-by-type")
    @ApiOperation({ summary: "Get all contracts for a company by type" })
    @ApiResponse({ status: 200, description: "Contracts retrieved successfully." })
    @ApiQuery({
        name: "projectType",
        required: true,
        type: String,
        description: "project type UUID",
    })
    findAllContractsByType(@GetUser() user: JwtUserPayload, @Query("projectType") projectType: string) {
        return this.companyService.findAllContracts(user.companyId, projectType.split(","));
    }

    @ApiOperation({ summary: "Get Tags" })
    @ApiOkResponse({ description: "List of tags retrieved successfully" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("media-setting")
    async getMediaSettings(@GetUser() user: JwtUserPayload) {
        return this.companyService.getMediaSettings(user.companyId);
    }

    @ApiOperation({ summary: "Get Tags" })
    @ApiOkResponse({ description: "List of tags retrieved successfully" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Get("tags")
    async getTagsList(@GetUser() user: JwtUserPayload) {
        return this.companyService.getTagsList(user.companyId);
    }

    @ApiOperation({ summary: "Update Tags" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Patch("media-setting")
    async updateTags(@GetUser() user: JwtUserPayload, @Body() updateTagDto: UpdateTagDto) {
        return this.companyService.updateMediaSetting(user.companyId, updateTagDto);
    }

    @ApiOperation({ summary: "Delete Tag" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Delete("tag")
    async deleteTag(
        @GetUser() user: JwtUserPayload,
        @Body() deleteTagDto: DeleteTagDto,
    ): Promise<HttpResponse> {
        return this.companyService.deleteTag(user.companyId, deleteTagDto);
    }

    @ApiOperation({ summary: "Create Action" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Post("sales-action")
    async createSalesAction(
        @GetUser() user: JwtUserPayload,
        @Body() createSalesActionDto: CreateSalesActionCompanyDto,
    ) {
        return this.companyService.addAction(user.companyId, createSalesActionDto);
    }

    @ApiOperation({ summary: "Update Action" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Patch("sales-action")
    async updateSalesAction(
        @GetUser() user: JwtUserPayload,
        @Body() updateSalesActionDto: UpdateSalesActionDto,
    ) {
        return this.companyService.updateSalesAction(user.companyId, updateSalesActionDto);
    }

    @ApiOperation({ summary: "Delete Sales Action" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Delete("sales-action")
    async deleteSalesAction(
        @GetUser() user: JwtUserPayload,
        @Body() deleteSalesActionDto: DeleteSalesActionDto,
    ): Promise<HttpResponse> {
        return this.companyService.deleteSalesAction(user.companyId, deleteSalesActionDto);
    }

    @ApiOperation({ summary: "Get Sales Action of company default" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("sales-action-default")
    async getSalesActionById(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.companyService.getSalesActionOfCompanyDefault(user._id, user.companyId);
    }

    @ApiOperation({ summary: "Create & Update content block for company" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Post("content")
    async upsertContent(
        @GetUser() user: JwtUserPayload,
        @Body() contentBlockDto: CreateContentBlockDto,
    ): Promise<HttpResponse> {
        return this.companyService.createContentBlock(user.companyId, user.memberId, contentBlockDto);
    }

    @ApiOperation({ summary: "Create & Update content block for company" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Patch("content")
    async updateContentBlock(
        @GetUser() user: JwtUserPayload,
        @Body() contentBlockDto: UpdateContentBlockDto,
    ): Promise<HttpResponse> {
        return this.companyService.updateContentBlock(user.companyId, contentBlockDto);
    }

    @ApiOperation({ summary: "Get all content block of company" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("content-all/deleted/:deleted")
    async getAllContent(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getContentDto: GetContentDto,
    ): Promise<HttpResponse> {
        return this.companyService.getAllContentBlock(user.companyId, deleted, getContentDto);
    }

    @ApiOperation({ summary: "Get content block by id of a company" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Get("content/:id")
    async getContentById(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.companyService.getContentBlockById(user.companyId, id);
    }

    @ApiOperation({ summary: "Delete content block" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Delete("content/:id")
    async deleteContentBlockById(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.companyService.deleteContentBlockById(user.companyId, id);
    }

    @ApiOperation({ summary: "Restore content block" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Auth()
    @Patch("restore-content/:id")
    async restoreContentBlockById(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.companyService.restoreContentBlockById(user.companyId, id);
    }
}
