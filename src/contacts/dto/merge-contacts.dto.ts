import { CreateNewActionDto } from "./create-new-action.dto";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean } from "class-validator";

export class MergeContactsDto {
    @ApiPropertyOptional({ description: "Business name of the contact" })
    @IsOptional()
    @IsString()
    businessName?: string;

    @ApiProperty({ description: "Full name of the contact" })
    @IsString()
    @IsOptional()
    fullName: string;

    @ApiProperty({ description: "First name of the contact" })
    @IsString()
    @IsOptional()
    firstName: string;

    @ApiProperty({ description: "Last name of the contact" })
    @IsString()
    @IsOptional()
    lastName: string;

    @ApiPropertyOptional({ description: "Phone number of the contact" })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiPropertyOptional({ description: "Email of the contact" })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: "Street address of the contact" })
    @IsOptional()
    @IsString()
    street?: string;

    @ApiPropertyOptional({ description: "City of the contact" })
    @IsOptional()
    @IsString()
    city?: string;

    @ApiPropertyOptional({ description: "State of the contact" })
    @IsOptional()
    @IsString()
    state?: string;

    @ApiPropertyOptional({ description: "Zip code of the contact" })
    @IsOptional()
    @IsString()
    zip?: string;

    @ApiPropertyOptional({ description: "Action to merge" })
    @IsOptional()
    action?: CreateNewActionDto;

    @ApiPropertyOptional({ description: "Type of contact" })
    @IsOptional()
    @IsString()
    type?: string;

    @ApiPropertyOptional({ description: "Is business contact" })
    @IsOptional()
    @IsBoolean()
    isBusiness?: boolean;

    @ApiPropertyOptional({ description: "Address of the contact" })
    @IsOptional()
    @IsString()
    address?: string;
}
